<template>
    <div>
        <div class="lg:flex lg:items-center lg:justify-between border-b pb-8 mb-8">
            <div class="min-w-0 flex-1">
                <h2 class="mt-2 text-2xl/7 font-bold text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
                    Withdraw
                </h2>
            </div>
        </div>

        <!-- Balance Display -->
        <div class="max-w-2xl mx-auto mb-8">
            <div class="bg-white p-6 rounded-lg shadow">
                <div class="flex flex-col items-start gap-1">
                    <span class="text-lg font-medium text-gray-600">Available Balance</span>
                    <span class="text-3xl font-bold text-green-700">${{ balance }} (USD)</span>
                </div>
            </div>
        </div>

        <div class="max-w-2xl mx-auto">
            <div class="bg-white p-6 rounded-lg shadow">
                <form @submit.prevent="submitForm">
                    <!-- Amount Input -->
                    <div class="mb-6">
                        <label for="amount" class="block text-sm font-medium text-gray-700 mb-2">
                            Withdrawal Amount (USD)
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <span class="text-gray-500 sm:text-sm">$</span>
                            </div>
                            <input type="number" id="amount" v-model="form.amount" class="block w-full pl-7 pr-12 rounded-md border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                :class="{ 'border-red-500': form.errors.amount }" placeholder="0.00" />
                        </div>
                    </div>
                    <div class="mb-6">
                        <label for="payment_method" class="block text-sm font-medium text-gray-700 mb-2">
                            <div class="flex justify-between items-center">
                                <span>Payment Method</span>
                                <Link :href="route('publisher.payment-settings.index')" class="text-indigo-600 hover:text-indigo-900 flex items-center gap-1">
                                <Pencil class="h-5 w-5" />
                                <span>Edit Payment Method</span>
                                </Link>
                            </div>
                        </label>
                        <select id="payment_method" v-model="form.payment_method_id" class="block w-full rounded-md border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                            :class="{ 'border-red-500': form.errors.payment_method_id }">
                            <option value="">Select a payment method</option>
                            <option v-for="method in paymentSettings" :key="method.id" :value="method.id">
                                {{ method.key.toUpperCase() }} - {{ getPaymentMethodDetails(method) }}
                            </option>
                        </select>
                        <p v-if="form.errors.payment_method_id" class="mt-1 text-sm text-red-600">
                            {{ form.errors.payment_method_id }}
                        </p>
                    </div>
                    <!-- Submit Button -->
                    <div>
                        <button type="submit"
                            class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
                            :disabled="form.processing">
                            <span v-if="form.processing">Processing...</span>
                            <span v-else>Withdraw Funds</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Withdrawal Requests History -->
        <div class="mt-8">
            <div class="bg-white p-6 rounded-lg shadow">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Withdrawal History</h3>
                <div class="overflow-x-auto">
                    <table class="w-full divide-y divide-gray-200" v-if="withdrawalRequests && withdrawalRequests.length > 0">
                        <thead>
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    ID
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Amount
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Status
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Date
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Details
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr v-for="request in withdrawalRequests" :key="request.id" class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    #{{ request.id }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    ${{ request.amount }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span :class="[
                                        'px-2 inline-flex text-xs leading-5 font-semibold rounded-full',
                                        {
                                            'bg-yellow-100 text-yellow-800': request.status === 'pending',
                                            'bg-green-100 text-green-800': request.status === 'approved',
                                            'bg-red-100 text-red-800': request.status === 'rejected'
                                        }
                                    ]">
                                        {{ request.status.charAt(0).toUpperCase() + request.status.slice(1) }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ request.created_at }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <button @click="showRequestDetails(request)" class="text-indigo-600 hover:text-indigo-900">
                                        View Details
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <div v-else>
                        <NoDataMessage message1="No withdrawal records found." />
                    </div>
                </div>
            </div>
        </div>

        <!-- Request Details Modal -->
        <div v-if="selectedRequest" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center">
            <div class="bg-white rounded-lg p-6 max-w-lg w-full mx-4">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Withdrawal Request Details</h3>
                    <button @click="selectedRequest = null" class="text-gray-400 hover:text-gray-500">
                        <span class="sr-only">Close</span>
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="space-y-4">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Request ID</dt>
                        <dd class="mt-1 text-sm text-gray-900">#{{ selectedRequest.id }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Amount</dt>
                        <dd class="mt-1 text-sm text-gray-900">${{ selectedRequest.amount }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Status</dt>
                        <dd class="mt-1">
                            <span :class="[
                                'px-2 inline-flex text-xs leading-5 font-semibold rounded-full',
                                {
                                    'bg-yellow-100 text-yellow-800': selectedRequest.status === 'pending',
                                    'bg-green-100 text-green-800': selectedRequest.status === 'approved',
                                    'bg-red-100 text-red-800': selectedRequest.status === 'rejected'
                                }
                            ]">
                                {{ selectedRequest.status.charAt(0).toUpperCase() + selectedRequest.status.slice(1) }}
                            </span>
                        </dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Payment Method</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ selectedRequest?.payment_method?.key?.toUpperCase() ||
                            'Stripe' }}
                        </dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Requested At</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ selectedRequest.created_at }}</dd>
                    </div>
                    <div v-if="selectedRequest.admin_notes">
                        <dt class="text-sm font-medium text-gray-500">Admin Notes</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ selectedRequest.admin_notes }}</dd>
                    </div>
                    <div v-if="selectedRequest.payment_url">
                        <dt class="text-sm font-medium text-gray-500">Payment Proof URL</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            <a :href="selectedRequest.payment_url" target="_blank" class="text-indigo-600 hover:text-indigo-900 flex items-center">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                </svg>
                                View Payment Proof
                            </a>
                        </dd>
                    </div>
                    <div v-if="selectedRequest.media && selectedRequest.media.length > 0">
                        <dt class="text-sm font-medium text-gray-500">Payment Proof Files</dt>
                        <dd class="mt-4 space-y-4">
                            <div v-for="file in selectedRequest.media" :key="file.id" class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                <div class="flex items-center space-x-4">
                                    <div class="flex-shrink-0">
                                        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                                        </svg>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">{{ file.name }}</p>
                                        <div class="flex items-center space-x-2 text-xs text-gray-500">
                                            <span>{{ formatFileSize(file.size) }}</span>
                                            <span>•</span>
                                            <span>{{ getFileExtension(file.name) }}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="flex-shrink-0">
                                    <a :href="file.url" target="_blank" rel="noopener noreferrer"
                                        class="inline-flex items-center px-2.5 py-1.5 text-xs font-medium text-emerald-700 bg-emerald-50 hover:bg-emerald-100 rounded-md transition-colors duration-200">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                        </svg>
                                        View
                                    </a>
                                </div>
                            </div>
                        </dd>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { useForm } from "@inertiajs/vue3";
import { inject, onMounted, ref } from 'vue';
import NoDataMessage from '@/Components/NoDataMessage.vue';



const props = defineProps({
    paymentSettings: {
        type: Array,
        required: true
    },
    balance: {
        type: Number,
        required: true
    },
    withdrawalRequests: {
        type: Array,
        required: true
    }
});

const notify = inject('$notify');
const selectedRequest = ref(null);

const form = useForm({
    amount: '',
    payment_method_id: ''
});

// Select default payment method on mount
onMounted(() => {
    const defaultMethod = props.paymentSettings.find(method => method.is_default);
    if (defaultMethod) {
        form.payment_method_id = defaultMethod.id;
    }
});

const validateForm = () => {
    const errors = {};

    // Validate amount
    if (!form.amount) {
        errors.message = 'Amount is required';
    } else if (isNaN(form.amount) || form.amount <= 0) {
        errors.message = 'Amount must be greater than 0';
    } else if (form.amount > props.balance) {
        errors.message = 'Amount cannot exceed available balance';
    }
    return errors;
};

const submitForm = () => {
    const errors = validateForm();

    if (Object.keys(errors).length > 0) {
        return notify(errors.message, { type: 'error' });
    }

    form.post(route('wallet.withdraw.store'), {
        preserveScroll: true,
        data: {
            amount: form.amount,
            payment_method_id: form.payment_method_id
        },
        onSuccess: () => {
            notify('Withdrawal request submitted successfully.', { type: 'success' });
            form.reset();
        },
        onError: (errors) => {
            form.errors = errors;
            const firstError = Object.values(errors)[0];
            notify(firstError || 'Failed to process withdrawal request.', { type: 'error' });
        },
    });
};

const getPaymentMethodDetails = (method) => {
    if (!method) return '';

    switch (method.key) {
        case 'paypal':
            return method.value.email;
        case 'bank':
            return `${method.value.bank_name} - ${method.value.account_number}`;
        case 'payoneer':
            return method.value.email;
        default:
            return '';
    }
};

const showRequestDetails = (request) => {
    selectedRequest.value = request;
};

const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const getFileExtension = (filename) => {
    return filename.split('.').pop().toUpperCase();
};
</script>

<style lang="scss" scoped></style>