{{-- MENU DESKTOP --}}
<nav class="relative z-10 w-fit" x-data="{
                  navigationMenuOpen: false,
                  navigationMenu: '',
                  navigationMenuCloseDelay: 200,
                  navigationMenuCloseTimeout: null,

                  navigationMenuLeave() {
                     let that = this;
                     this.navigationMenuCloseTimeout = setTimeout(() => {
                        that.navigationMenuClose();
                     }, this.navigationMenuCloseDelay);
                  },
                  
                  navigationMenuReposition(navElement) {
                     this.navigationMenuClearCloseTimeout();
                     this.$refs.navigationDropdown.style.left = navElement.offsetLeft + 'px';
                     this.$refs.navigationDropdown.style.marginLeft = (navElement.offsetWidth/2) + 'px';
                  },

                  navigationMenuClearCloseTimeout(){
                     clearTimeout(this.navigationMenuCloseTimeout);
                  },

                  navigationMenuClose(){
                     this.navigationMenuOpen = false;
                     this.navigationMenu = '';
                  }
               }">




   <div class="relative hidden md:flex ">
      <ul class="flex items-center justify-center flex-1 p-1 space-x-1 list-none text-muted-foreground group">


         @if (auth()->user()->role == 'advertiser')
         <li>
            <a href="{{route('advertiser.dashboard')}}" wire:navigate class="top-menu-main-link group
                  {{ menu_active_link(['advertiser.dashboard']) }}">
               Dashboard
            </a>
         </li>
         @endif





         <li>
            <a href="{{route('marketplace')}}" wire:navigate class="top-menu-main-link group
               {{ menu_active_link(['marketplace']) }}">
               Marketplace
            </a>
         </li>

         @if (auth()->user()->role == 'advertiser')
         <li>
            <a href="{{route('advertiser.my-orders')}}" wire:navigate
               class="top-menu-main-link group
               {{ menu_active_link(['advertiser.my-orders', 'advertiser.order-details', 'advertiser.order-item-details']) }}">
               My Orders
            </a>
         </li>
         <li>
            <a href="{{route('advertiser.guidelines')}}" wire:navigate
               class="top-menu-main-link group
               {{ menu_active_link(['advertiser.guidelines']) }}">
               My Guidelines
            </a>
         </li>
         @endif

         <li class="hidden">
            <button
               :class="{ 'bg-neutral-100' : navigationMenu=='getting-started', 'hover:bg-neutral-100' : navigationMenu!='getting-started' }"
               @mouseover="navigationMenuOpen=true; navigationMenuReposition($el); navigationMenu='getting-started'"
               @mouseleave="navigationMenuLeave()" class="top-menu-main-link group">

               <span>More</span>

               <svg :class="{ '-rotate-180' : navigationMenuOpen==true && navigationMenu == 'getting-started' }"
                  class="relative top-[1px] ml-1 h-3 w-3 ease-out duration-300" xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                  stroke-linejoin="round" aria-hidden="true">
                  <polyline points="6 9 12 15 18 9"></polyline>
               </svg>
            </button>
         </li>
      </ul>
   </div>






   <div x-ref="navigationDropdown" x-show="navigationMenuOpen" x-transition:enter="transition ease-out duration-100"
      x-transition:enter-start="opacity-0 scale-90" x-transition:enter-end="opacity-100 scale-100"
      x-transition:leave="transition ease-in duration-100" x-transition:leave-start="opacity-100 scale-100"
      x-transition:leave-end="opacity-0 scale-90" @mouseover="navigationMenuClearCloseTimeout()"
      @mouseleave="navigationMenuLeave()"
      class="absolute top-0 pt-3 duration-200 ease-out -translate-x-1/2 translate-y-11 opacity-0"
      x-bind:class="navigationMenuOpen ? 'opacity-100' : 'opacity-0'" x-cloak>

      <div
         class="flex justify-center w-auto h-auto overflow-hidden bg-white border rounded-md shadow-sm border-neutral-200/70">

         <div x-show="navigationMenu == 'getting-started'"
            class="flex items-stretch justify-center w-full max-w-2xl p-6 gap-x-3">

            <div class="flex flex-shrink-0 w-48 rounded pb-7 bg-gradient-to-br from-neutral-800 to-black">
               <div class="flex flex-col relative px-6 space-y-1.5 text-white content-center 
                           items-center self-end ">
                  <x-icons.etc.bear class="fill-white w-8 h-8" />
                  <span class="block font-bold text-lg">
                     PressBear
                  </span>
                  <span class="block text-sm opacity-80 text-center leading-relaxed">
                     Your One Stop Solution For Sponsorships
                  </span>
               </div>
            </div>

            <div class="w-72">
               <a href="{{ route('home') }}" class="block px-3.5 py-3 text-sm rounded hover:bg-neutral-100">
                  <span class="top-menu-sub-link-label  ">
                     Homepage
                  </span>
                  <span class="top-menu-sub-link-description">
                     Go To Homepage
                  </span>
               </a>

               <a href="#_" @click="navigationMenuClose()"
                  class="block px-3.5 py-3 text-sm rounded hover:bg-neutral-100">
                  <span class="top-menu-sub-link-label">
                     Guide
                  </span>
                  <span class="top-menu-sub-link-description">
                     Docs to learn mores.
                  </span>
               </a>

               <a href="#_" @click="navigationMenuClose()"
                  class="block px-3.5 py-3 text-sm rounded hover:bg-neutral-100">
                  <span class="top-menu-sub-link-label">Support</span>
                  <span class="top-menu-sub-link-description">
                     Need help? Feel free to reach out to us at {{ env('SUPPORT_EMAIL') }}
                  </span>
               </a>
            </div>
         </div>


         {{-- <div x-show="navigationMenu == 'learn-more'" class="flex items-stretch justify-center w-full p-2">

            <div class="w-72">

               <a href="" @click="navigationMenuClose()" class="top-menu-sub-link">
                  <div class="icon-top-menu-sub-link  flex items-center mr-4">
                     <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2"
                        stroke="currentColor" class="icon-top-menu-sub-link-svg">
                        <path stroke-linecap="round" stroke-linejoin="round"
                           d="M20.893 13.393l-1.135-1.135a2.252 2.252 0 01-.421-.585l-1.08-2.16a.414.414 0 00-.663-.107.827.827 0 01-.812.21l-1.273-.363a.89.89 0 00-.738 1.595l.587.39c.59.395.674 1.23.172 1.732l-.2.2c-.212.212-.33.498-.33.796v.41c0 .409-.11.809-.32 1.158l-1.315 2.191a2.11 2.11 0 01-1.81 1.025 1.055 1.055 0 01-1.055-1.055v-1.172c0-.92-.56-1.747-1.414-2.089l-.655-.261a2.25 2.25 0 01-1.383-2.46l.007-.042a2.25 2.25 0 01.29-.787l.09-.15a2.25 2.25 0 012.37-1.048l1.178.236a1.125 1.125 0 001.302-.795l.208-.73a1.125 1.125 0 00-.578-1.315l-.665-.332-.091.091a2.25 2.25 0 01-1.591.659h-.18c-.249 0-.487.1-.662.274a.931.931 0 01-1.458-1.137l1.411-2.353a2.25 2.25 0 00.286-.76m11.928 9.869A9 9 0 008.965 3.525m11.928 9.868A9 9 0 118.965 3.525" />
                     </svg>
                  </div>
                  <div>
                     <span class="top-menu-sub-link-label">
                        Domain Report
                     </span>
                     <span class="top-menu-sub-link-description">
                        Check the quality of the domain before you build the links.
                     </span>
                  </div>
               </a>


               <a href="" @click="navigationMenuClose()" class="top-menu-sub-link">
                  <div class="icon-top-menu-sub-link flex items-center mr-4">
                     <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                        stroke="currentColor" class="w-6 h-6">
                        <path stroke-linecap="round" stroke-linejoin="round"
                           d="M9 12.75L11.25 15 15 9.75m-3-7.036A11.959 11.959 0 013.598 6 11.99 11.99 0 003 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285z" />
                     </svg>
                  </div>
                  <div>
                     <span class="top-menu-sub-link-label">
                        Backlink Report
                     </span>
                     <span class="top-menu-sub-link-description">
                        Check the quality of backlink to optimise it for maximum link juice.
                     </span>
                  </div>
               </a>


               <a href="" @click="navigationMenuClose()" class="top-menu-sub-link">
                  <div class="icon-top-menu-sub-link flex items-center mr-4">
                     <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                        stroke="currentColor" class="icon-top-menu-sub-link-svg">
                        <path stroke-linecap="round" stroke-linejoin="round"
                           d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10" />
                     </svg>
                  </div>
                  <div>
                     <span class="top-menu-sub-link-label">
                        Content Optimisation Tool
                     </span>
                     <span class="top-menu-sub-link-description">
                        A tool to help you optimise your content for better ranking chance.
                     </span>
                  </div>
               </a>


            </div>
         </div> --}}
      </div>
   </div>


   {{-- Mobile Menu --}}
   @if (auth()->user()->role == 'advertiser')
   <div class="flex text-gray-600 md:hidden mx-2 md:mx-4 text-center">
      <a href="{{route('advertiser.my-orders')}}" wire:navigate
         class="text-xs py-1 hover:bg-gray-100 focus:bg-gray-100 text-gray-600 px-3 border rounded-lg text-nowrap font-medium ml-0 sm:ml-4">My
         Orders</a>
   </div>
   @endif

</nav>