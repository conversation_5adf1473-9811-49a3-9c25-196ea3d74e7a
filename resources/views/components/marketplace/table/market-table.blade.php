<!-- Table Section Preline - Marketplace -->
<div class="border border-gray-200 rounded-xl shadow bg-white">

  <!-- Card -->
  <div class="flex flex-col ">
    <div class="-mx-2 ">
      <div class="p-2 align-middle ">
        <div class="bg-white dark:bg-slate-900 dark:border-gray-700 ">


          {{-- Table Header Section Start --}}
          <div class="header-table">

            {{-- Head Bar --}}
            <div class="flex flex-col md:flex-row items-center justify-between 
                          space-y-3 md:space-y-0 md:space-x-4 p-4">


              {{-- SEARCH SECTION --}}
              <div id="search-table" class="w-full md:w-1/2 flex" x-data="{
                                  
                                  search(){
                                    $store.search.active = true

                                    if($store.search.term.length >= 2){
                                      updateTable() 
                                    }
                                  },

                                  cancelSearch(){
                                    $store.search.active = false,
                                    $store.search.term = null,
                                    updateTable() 
                                  }
                        }">

                <label for="simple-search" class="sr-only">Search</label>
                <div class="relative w-full">

                  {{-- search icon left --}}
                  <div class="text-gray-800 dark:text-gray-400 absolute inset-y-0 
                                    left-0 flex items-center pl-3 pointer-events-none">
                    <span class="w-5 h-5">
                      <x-icons.etc.search />
                    </span>
                  </div>


                  {{-- Search Input --}}
                  <form @submit.prevent="search()">
                    <input type="text" {{-- @keyup.once="searchActionButton = !searchActionButton" --}} {{--
                      @mouseleave="searchActionButton = !searchActionButton" --}} x-model="$store.search.term"
                      name="search" id="table-search"
                      class="bg-gray-50  text-gray-800 text-sm 
                                         rounded-lg focus:ring-emerald-600  block w-full pl-10 p-2 
                                         dark:bg-gray-700 dark:border-gray-600 
                                         dark:placeholder-gray-400
                                         dark:text-white dark:focus:ring-emerald-500 dark:focus:border-primary-500 border" :class="$store.search.active ? 'border-orange-400' : 
                                                                 'border-gray-300 ' "
                      placeholder="Search website or topic...">
                  </form>


                  {{-- search action button --}}
                  <div x-cloak x-show='$store.search.term' x-transition @click.prevent="search()" title="search"
                    class="text-gray-800 dark:text-gray-400 absolute right-0 inset-y-0 flex items-center px-5 m-0.5  rounded-r-lg  bg-gray-200 cursor-pointer  hover:text-white hover:bg-emerald-600">
                    <span class="w-4 h-4">
                      <x-icons.lucide.arrow-right />
                    </span>
                  </div>


                  {{-- search cancel button --}}
                  <div x-cloak @click.prevent="cancelSearch()" title="cancel search" x-show='$store.search.active'
                    class=" absolute right-0
                                  w-5 h-5 p-1
                                  items-center
                                  justify-center
                                  inset-y-0 flex 
                                  rounded-lg
                                  -mt-2 -mr-2 bg-orange-600
                                  hover:bg-orange-800
                                  cursor-pointer">
                    <span class="w-3 text-white">
                      <x-icons.lucide.trash />
                    </span>
                  </div>

                </div>

              </div>




              {{-- Right Section Buttons: checkout + review --}}
              <div
                class="hidden md:flex w-full md:w-auto flex-col md:flex-row space-y-2 md:space-y-0 items-stretch md:items-center justify-end md:space-x-3 flex-shrink-0">
                <a href="{{route('cart')}}" wire:navigate class="cursor-pointer">
                  <button type="button"
                    class="flex items-center justify-center px-4 py-2 text-sm font-medium text-white rounded-lg bg-orange-600 hover:bg-orange-700 focus:ring-2 focus:ring-offset-2 focus:ring-orange-600 dark:bg-primary dark:hover:bg-primary focus:outline-none dark:focus:ring-primary ">
                    <x-icons.iconly.cart width="18" height="18" />
                    <span class="ml-2">Checkout</span>
                  </button>
                </a>
              </div>

            </div>




            {{-- Secondary Bar --}}
            <div
              class="flex flex-col md:flex-row justify-between px-5 py-2 space-y-3 lg:flex-row  md:items-center lg:justify-between lg:space-y-0 lg:space-x-4 mb-3">

              <div id="cart-stats"
                class="border p-2 rounded  md:border-0 md:p-0 grid grid-cols-3 md:flex items-center text-sm font-medium">

                <div class="mr-6">
                  <span class="text-gray-500">Results:</span>
                  <span id="available-sites" class="text-emerald-900 font-semibold">
                    {{ Number::format($websites->total()) }}
                  </span>
                </div>


                <div class="mr-6">
                  <span class="text-gray-500">In Cart:</span>
                  <span id="marketTableStatCount" class="text-emerald-900 font-semibold">
                    {{ $cartData['stats']['count'] }}
                  </span>
                </div>
                <div>
                  <span class="text-gray-500">Total Price:</span>
                  <span class="text-emerald-900 font-semibold">
                    $<span id="marketTableStatPrice">{{ Number::format($cartData['stats']['totalPrice']) }}</span>
                  </span>
                </div>
              </div>


              <div class="flex flex-col flex-shrink-0 md:flex-row md:items-center lg:justify-end 
                            md:space-y-0">

                <div x-data="{

                          actionDisply: false, 
                          filterDisplay: false, 
                          postTypeDisply: false,

                          changeNiche(type){
                            $store.niche.type = type,
                            $store.niche.active = true,
                            this.postTypeDisply = false,

                            updateTable()
                          }
                        }" class="flex mt-2 md:mt-0">


                  <button @click="postTypeDisply = ! postTypeDisply" id="postTypesDropdownButton"
                    data-dropdown-toggle="postTypesDropdown" class="w-1/2 md:w-auto flex items-center justify-center py-2 px-4 
                                text-sm font-medium text-gray-800 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100
                                hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-orange-100 
                                dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 
                                dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700" type="button">
                    <span class="mr-1 text-gray-600">Niche:</span>
                    <span class="mr-2 text-green-800 font-semibold capitalize" x-text="$store.niche.type">General</span>

                    <svg class="-ml-1 mr-1.5 w-4 h-4" fill="currentColor" viewbox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                      <path clip-rule="evenodd" fill-rule="evenodd"
                        d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
                    </svg>
                  </button>

                  <div class="w-1/2 md:hidden ml-2">
                    <a href="{{route('cart')}}" wire:navigate class="cursor-pointer">
                      <button type="button"
                        class="flex items-center w-full justify-center px-4 py-2 text-sm font-medium text-white rounded-lg bg-orange-600 hover:bg-orange-700 focus:ring-2 focus:ring-offset-2 focus:ring-orange-600 dark:bg-primary dark:hover:bg-primary focus:outline-none dark:focus:ring-primary ">
                        <x-icons.iconly.cart width="18" height="18" />
                        <span class="ml-2">Checkout</span>
                      </button>
                    </a>
                  </div>



                  {{-- postTypes Dropdown --}}
                  <div x-show="postTypeDisply" x-cloak @click.away="postTypeDisply=false" id="postTypesDropdown"
                    class="absolute mt-10 z-50 hover:cursor-pointer border rounded-lg shadow-xl w-auto md:min-w-32  overflow-hidden">

                    <div class="  dark:bg-gray-700 dark:divide-gray-600 ">
                      <ul class="bg-white divide-y-2 divide-gray-100 text-sm text-gray-700 dark:text-gray-200 ">
                        <li @click="changeNiche('general')" :class="$store.niche.type == 'general' && 'font-semibold'"
                          class="flex items-center py-3 px-4 hover:bg-gray-100 hover:text-emerald-700 
                                    dark:hover:bg-gray-600 dark:hover:text-white">

                          <x-icons.lucide.anchor class="w-3.5 h-3.5  mr-2 opacity-80" />
                          General
                        </li>
                        <li @click="changeNiche('casino')"
                          :class="$store.niche.type == 'casino' && 'font-semibold bg-gray-100'" class="flex items-center py-3 px-4 hover:bg-gray-100 hover:text-red-700 
                                    dark:hover:bg-gray-600 dark:hover:text-white">
                          <x-icons.lucide.dices class="w-3.5 h-3.5  mr-2 opacity-80" />
                          Casino
                        </li>
                        <li @click="changeNiche('crypto')"
                          :class="$store.niche.type == 'crypto' && 'font-semibold bg-gray-100'" class="flex items-center py-3 px-4 hover:bg-gray-100 hover:text-yellow-700 
                                    dark:hover:bg-gray-600 dark:hover:text-white">
                          <x-icons.lucide.bitcoin class="w-3.5 h-3.5  mr-2 opacity-80" />
                          Crypto
                        </li>
                        <li @click="changeNiche('dating')"
                          :class="$store.niche.type == 'dating' && 'font-semibold bg-gray-100'" class="flex items-center py-3 px-4 hover:bg-gray-100 hover:text-orange-700 
                                    dark:hover:bg-gray-600 dark:hover:text-white">
                          <x-icons.lucide.heart class="w-3 h-3  mr-2 opacity-80" />
                          Dating
                        </li>
                        <li @click="changeNiche('adult')"
                          :class="$store.niche.type == 'adult' && 'font-semibold bg-gray-100'" class="flex items-center py-3 px-4 hover:bg-gray-100 hover:text-purple-700 
                                    dark:hover:bg-gray-600 dark:hover:text-white">
                          <x-icons.lucide.banana class="w-3.5 h-3.5  mr-2 opacity-80" />
                          Adult
                        </li>
                        <li @click="changeNiche('cbd')"
                          :class="$store.niche.type == 'cbd' && 'font-semibold bg-gray-100'" class="flex items-center py-3 px-4 hover:bg-gray-100 hover:text-green-700 
                                    dark:hover:bg-gray-600 dark:hover:text-white">
                          <x-icons.lucide.cannabis class="w-3.5 h-3.5  mr-2 opacity-80" />
                          CBD
                        </li>
                      </ul>
                    </div>
                  </div>

                  {{--
                  <button @click="actionDisply = ! actionDisply" id="actionsDropdownButton"
                    data-dropdown-toggle="actionsDropdown"
                    class="w-full md:w-auto flex items-center justify-center py-2 px-4 text-sm font-medium text-gray-600 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700"
                    type="button">
                    <div class="w-4 h-4  mr-1 ">
                      <x-icons.lucide.star />
                    </div>
                    Favorites
                  </button> --}}
                </div>

              </div>
            </div>

          </div>
          {{-- Table Header Section End --}}



          {{-- Show progress bar during table updates --}}
          <div class="progress-wrap w-full">
            <div id="table-progress-bar" class="progressbar w-full"></div>
          </div>



          <!-- TABLE -->
          <div style="overflow-x:auto;"
            class="min-w-full bg-white min-h-[20vh] max-h-[85vh] border-t-4 border-zinc-150 ">

            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700 ">


              {{-- Table Head --}}
              <thead
                class="table-auto md:sticky text-xs md:top-0 z-30 bg-gray-100 dark:bg-slate-900 text-gray-600 text-center dark:text-gray-200 uppercase tracking-wide ">


                {{-- Heading Row --}}
                <tr>

                  <th scope="col" class="buy-quantity px-12 w-full py-8 z-40">
                    <div class="flex items-center gap-x-2 font-semibold">
                      Buy
                    </div>
                  </th>

                  <th scope="col" class="px-6 py-3">
                    <div class="flex items-center justify-center gap-x-2">

                      <div id="sorting" class="w-4 h-4 cursor-pointer" x-transition @click="sortUpdate('price')">

                        <span id="sort-icon" x-show="!$store.table.sort.active || 
                                              $store.table.sort.column != 'price'">
                          <x-icons.lucide.sort />
                        </span>

                        {{-- only show if colum=price and sort=active --}}
                        <span x-cloak x-show="$store.table.sort.active && 
                                              $store.table.sort.column == 'price'">

                          {{-- asc --}}
                          <span id="sort-asc-icon" class="text-gray-900" x-show="$store.table.sort.order == 'asc'">
                            <x-icons.lucide.sort-asc />
                          </span>

                          {{-- desc --}}
                          <span id="sort-desc-icon" class="text-gray-900" x-show="$store.table.sort.order == 'desc'">
                            <x-icons.lucide.sort-desc />
                          </span>
                        </span>

                      </div>

                      <span class="text-xs font-semibold">
                        Price
                      </span>
                      <span x-show="$store.niche.active" x-cloak class="text-[8px] capitalize text-amber-700">
                        (<span x-text="$store.niche.type"></span>)
                      </span>
                    </div>
                  </th>

                  <th scope="col" class="px-6 py-3">
                    <div class="flex items-center ml-6">
                      <span class="text-xs items-center font-semibold">
                        Website
                      </span>
                    </div>
                  </th>

                  <th scope="col" class="px-6 py-3">
                    <div class="flex items-center justify-center gap-x-2">

                      <div id="sorting-traffic" class="w-4 h-4 cursor-pointer" x-transition
                        @click="sortUpdate('traffic')">

                        <span id="sort-icon" x-show="!$store.table.sort.active || 
                                              $store.table.sort.column != 'traffic'">
                          <x-icons.lucide.sort />
                        </span>

                        {{-- only show if colum=price and sort=active --}}
                        <span x-cloak x-show="$store.table.sort.active && 
                                              $store.table.sort.column == 'traffic'">

                          {{-- asc --}}
                          <span id="sort-asc-icon" class="text-gray-900" x-show="$store.table.sort.order == 'asc'">
                            <x-icons.lucide.sort-asc />
                          </span>

                          {{-- desc --}}
                          <span id="sort-desc-icon" class="text-gray-900" x-show="$store.table.sort.order == 'desc'">
                            <x-icons.lucide.sort-desc />
                          </span>
                        </span>

                      </div>

                      <span class="text-xs font-semibold w-full">
                        Org. Traffic
                      </span>
                      <div class="tooltip">
                        <x-ui.tooltip text="?" tooltip="Organic Traffic from Google" />
                      </div>
                    </div>
                  </th>

                  <th scope="col" class="px-6 py-3">
                    <div class="flex items-center justify-center gap-x-2">

                      <div id="sorting-dr" class="flex w-4 h-4 text-gray-400 hover:text-gray-900 cursor-pointer"
                        x-transition @click="sortUpdate('dr')">

                        <span id="sort-icon" x-show="!$store.table.sort.active || 
                                              $store.table.sort.column != 'dr'">
                          <x-icons.lucide.sort />
                        </span>

                        {{-- only show if colum=price and sort=active --}}
                        <span x-cloak x-show="$store.table.sort.active && 
                                              $store.table.sort.column == 'dr'">

                          {{-- asc --}}
                          <span id="sort-asc-icon" class="text-gray-900" x-show="$store.table.sort.order == 'asc'">
                            <x-icons.lucide.sort-asc />
                          </span>

                          {{-- desc --}}
                          <span id="sort-desc-icon" class="text-gray-900" x-show="$store.table.sort.order == 'desc'">
                            <x-icons.lucide.sort-desc />
                          </span>
                        </span>

                      </div>


                      <span class="flex text-xs font-semibold">
                        Ahref DR
                      </span>
                      <div class="tooltip flex">
                        <x-ui.tooltip text="?" tooltip="Ahref Domain Rating For Site" />
                      </div>
                    </div>
                  </th>


                  <th scope="col" class="px-6 py-3">
                    <div class="flex items-center gap-x-2">
                      <span class="text-xs font-semibold">
                        SemRush AS
                      </span>
                      <div class="tooltip">
                        <x-ui.tooltip text="?" tooltip="SemRush Domain Rating For Site" />
                      </div>
                    </div>
                  </th>

                  <th scope="col" class="px-6 py-3">
                    <div class="flex items-center gap-x-2">
                      <div class="text-xs font-semibold">
                        Top Traffic Country
                      </div>
                      <div class="tooltip">
                        <x-ui.tooltip text="?" tooltip="Country with the highest traffic" />
                      </div>
                    </div>
                  </th>


                  <th scope="col" class="px-6 py-3">
                    <div class="flex items-center gap-x-2">
                      <span class="text-xs font-semibold">
                        Reffering Domains
                      </span>
                    </div>
                  </th>

                  <th scope="col" class="px-6 py-3">
                    <div class="flex items-center gap-x-2">
                      <span class="text-xs font-semibold">
                        Linked Domains
                      </span>
                    </div>
                  </th>

                  <th scope="col" class="px-6 py-3">
                    <div class="flex items-center gap-x-2">
                      <span class="text-xs font-semibold">
                        Language
                      </span>
                    </div>
                  </th>

                  <th scope="col" class="px-6 py-3">
                    <div class="flex items-center gap-x-2">
                      <span class="text-xs font-semibold">
                        Spam Score
                      </span>
                    </div>
                  </th>

                  {{-- <th scope="col" class="px-6 py-3">
                    <div class="flex items-center gap-x-2">
                      <span class="text-xs font-semibold">
                        Rating
                      </span>
                    </div>
                  </th>
                  --}}
                  {{-- <th scope="col" class="px-6 py-3">
                    <div class="flex items-center gap-x-2">
                      <span class="text-xs font-semibold">
                        Value
                      </span>
                    </div>
                  </th>
                  --}}
                  <th scope="col" class="px-6 py-3">
                    <div class="flex items-center gap-x-2">
                      <span class="text-xs font-semibold">
                        Relation
                      </span>
                      <div class="tooltip">
                        <x-ui.tooltip text="?" tooltip="Type of link relation site offer" />
                      </div>
                    </div>
                  </th>

                  <th scope="col" class="px-6 py-3">
                    <div class="flex items-center gap-x-2">
                      <span class="text-xs font-semibold">
                        Sponsorship Label
                      </span>
                    </div>
                  </th>

                  <th scope="col" class="px-6 py-3">
                    <div class="flex items-center gap-x-2">
                      <span class="text-xs font-semibold">
                        Category
                      </span>
                    </div>
                  </th>

                  <th scope="col" class="px-6 py-3">
                    <div class="flex items-center gap-x-2">
                      <span class="text-xs font-semibold">
                        Domain Age
                      </span>
                    </div>
                  </th>

                  {{--<th scope="col" class="px-6 py-3">
                    <div class="flex items-center gap-x-2">
                      <span class="text-xs font-semibold">
                        More
                      </span>
                    </div>
                  </th> --}}

                </tr>
              </thead>



              {{-- Table Body --}}
              <tbody class="divide-y-2 divide-gray-100 dark:divide-gray-700" x-data="{activeInfo: -1,

                                  activeInfoUpdate(id){
                                    if(id == this.activeInfo){
                                      this.activeInfo = -1
                                    }else{
                                      this.activeInfo = id
                                    }
                                  }
                        }">



                @foreach($websites as $website)

                <tr @class(["bg-gray-50/70"=> $loop->even, "row-table, hover:bg-gray-100"])
                  :class="{'shadow-md' : (activeInfo == {{ $website->id }}) }"
                  key="{{ $website->id }}">

                  <td class="buy-quantity md:sticky md:left-0 bg-white">
                    <div class="my-3 mx-5">


                      <div class="flex items-center" x-data="{

                                {{-- 3 Buttons: buy, incart, remove --}}
                                displayButton: 'buy',
                                buttonDisabled: false,


                                {{-- display based on cart status --}}
                                @if(in_array($website->id, $cartWebsitesids))
                                      inCart: true,
                                      displayButton: 'inCart',
                                @else
                                    inCart: false,
                                    displayButton: 'buy',
                                @endif

                                
                                addToCart(){

                                  this.buttonDisabled = true,

                                  axios.post('{{route('add-to-cart')}}', {
                                        {{-- _token: '{{ csrf_token() }}', --}}
                                        website: '{{$website->id}}',
                                        niche: $store.niche.type
                                      }).then((response) => {
                                            this.displayButton='inCart',
                                            this.inCart=true,
                                            cartPreview=true,
                                            this.buttonDisabled = false,
                                            toast('Added To Cart', {type: 'success', position: 'bottom-center'}),
                                            updateMarketCartPreview()
                                          } 
                                      )
                                },


                                removeFromCart(){
                                  
                                  this.buttonDisabled = true,

                                  axios.post('{{route('update-cart')}}', {
                                    _token: '{{ csrf_token() }}',
                                    task: 'deleteCartItem',
                                    cartWebsiteId: '{{$website->id}}'
                                  }).then((response) => { 
                                      this.inCart=false,
                                      this.displayButton='buy',
                                      {{-- cartPreview=false, --}}
                                      this.buttonDisabled = false,
                                      toast('Removed From Cart', {type: 'info', position: 'bottom-center'}),
                                      updateMarketCartPreview()
                                    }
                                  )
                                },


                                removeButtonShow(){
                                  this.displayButton='remove'
                                },


                                removeButtonHide(){
                                  if(this.inCart == false){
                                    this.displayButton='buy'
                                  } else{
                                    this.displayButton='inCart'
                                  }
                                },


                              }">

                        {{-- Buy Button --}}
                        <button id="buy" {{-- x-cloak --}} x-show="displayButton == 'buy'" type="button"
                          class="flex w-full bg-emerald-600 text-white hover:bg-emerald-700 hover:border-emerald-700 justify-center  items-center px-2 py-2 text-center rounded-md text-sm font-medium  shadow-sm transition duration-150 ease-in-out z-40"
                          x-bind:disabled="buttonDisabled" @click.prevent="addToCart">
                          <span x-show="buttonDisabled" x-cloak>
                            <x-icons.animated.spin class="w-3 h-3 mr-1 inline" />
                          </span>
                          <span x-show="!buttonDisabled">+ Buy</span>
                        </button>

                        {{-- Incart Button --}}
                        <button id="incart" x-cloak x-show="displayButton == 'inCart'" @mouseover="removeButtonShow"
                          @mouseleave="removeButtonHide" type="button" x-bind:disabled="buttonDisabled"
                          class="flex bg-white text-emerald-600 border-2 border-emerald-600 w-full items-center justify-center px-2 py-2 text-center rounded-md text-sm font-medium shadow-sm transition duration-150 ease-in-out z-40">
                          In Cart
                        </button>

                        {{-- Remove --}}
                        <button id="remove" @mouseover="removeButtonShow" @mouseleave="removeButtonHide" x-cloak
                          x-show="displayButton == 'remove'" type="button"
                          class="flex text-white bg-red-600 border-2 border-red-600 w-full items-center justify-center px-2 py-2 text-center rounded-md text-sm font-medium shadow-sm transition duration-150 ease-in-out z-40"
                          x-bind:disabled="buttonDisabled" @click.prevent="removeFromCart">
                          <span x-show="buttonDisabled" x-cloak>
                            <x-icons.animated.spin class="w-3 h-3 mr-1 inline" />
                          </span>
                          <span x-show="!buttonDisabled">Remove</span>
                        </button>

                      </div>

                    </div>
                  </td>


                  {{-- PRICE --}}
                  <td class="price">
                    <div class="px-6 py-4">
                      <span class="w-auto text-sm font-semibold text-gray-700 dark:text-gray-400">
                        <span x-show="$store.niche.type == 'general'" id="guest-post-price" class="">
                          ${{$website->guest_post_price}}
                        </span>
                        <span x-show="$store.niche.type == 'casino'" id="casino-post-price" x-cloak class="">
                          ${{$website->casino_post_price}}
                        </span>
                        <span x-show="$store.niche.type == 'crypto'" id="crypto-post-price" x-cloak class="">
                          ${{$website->crypto_post_price}}
                        </span>
                        <span x-show="$store.niche.type == 'adult'" id="adult-post-price" x-cloak class="">
                          ${{$website->adult_post_price}}
                        </span>
                        <span x-show="$store.niche.type == 'dating'" id="dating-post-price" x-cloak class="">
                          ${{$website->dating_post_price}}
                        </span>
                        <span x-show="$store.niche.type == 'cbd'" id="dating-post-price" x-cloak class="">
                          ${{$website->cbd_post_price}}
                        </span>
                      </span>
                    </div>
                  </td>



                  {{-- DOMAIN --}}
                  <td class="domain font-medium">
                    <div class="px-6 py-4">
                      <div class="flex items-center ">

                        {{-- Down icon --}}
                        <div class="flex cursor-pointer text-gray-400 p-1 mr-2 rounded-full hover:bg-gray-100"
                          @click="activeInfoUpdate({{ $website->id }})">
                          <svg class="w-4 h-4 duration-200 ease-out"
                            :class="{ 'rotate-180': activeInfo=={{ $website->id }} }" viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg" fill="none" stroke="currentColor" stroke-width="2"
                            stroke-linecap="round" stroke-linejoin="round">
                            <polyline points="6 9 12 15 18 9"></polyline>
                          </svg>
                        </div>

                        {{-- Image + Domain --}}
                        <div class="flex cursor-pointer" @click="activeInfoUpdate({{ $website->id }})">
                          <img loading="lazy"
                            src="https://www.google.com/s2/favicons?sz=64&domain_url={{$website->website_domain}}"
                            data-src="https://www.google.com/s2/favicons?sz=64&domain_url={{$website->website_domain}}"
                            alt="favicon" class="w-5 h-5 border rounded-full mr-2">
                          <span class="text-sm  text-gray-800 dark:text-gray-400 truncate">
                            {{str_replace('/', '', $website->website_domain)}}
                          </span>
                        </div>

                        {{-- External Icon --}}
                        <a class="flex ml-1.5 w-3 h-3 text-gray-700 hover:text-gray-900 external-link-icon"
                          target="_blank" href="https://{{$website->website_domain}}">
                          <x-icons.lucide.external-link />
                        </a>

                      </div>
                    </div>
                  </td>


                  {{-- ORG. TRAFFIC --}}
                  <td class="traffic px-6 py-4">
                    <span class="flex w-fit mx-auto text-sm text-gray-800 dark:text-gray-400">
                      @if($website->ahref_organic_traffic)
                      {{ Number::abbreviate($website->ahref_organic_traffic, 1) }}
                      @else
                      <span class="text-gray-300">No Data</span>
                      @endif
                    </span>
                  </td>


                  {{-- AHREF DR --}}
                  <td class="dr p-4">
                    <div
                      class="flex w-fit rounded-full font-medium cursor-default bg-blue-50 px-2.5 py-1.5 text-gray-800 text-xs mx-auto">
                      {{$website->ahref_domain_rank}}
                    </div>
                  </td>


                  {{-- SEMRUSH AS --}}
                  <td class="p-4">
                    <div
                      class="flex w-fit rounded-full font-medium cursor-default bg-orange-50 px-2.5 py-1.5 text-gray-800 text-xs mx-auto ">
                      {{$website->semrush_authority_score}}
                    </div>
                  </td>


                  {{-- Top Traffic Country --}}
                  <td class="country">
                    <div class="flex justify-center align-middle items-center  py-4">
                      @if($website->topTrafficCountryCode)
                      <img class="flex w-4 h-4 mr-1.5  border-gray-100 
                                         border object-contain"
                        src="{{asset('storage/graphics/country-flags/'. $website->topTrafficCountryCode .'.svg');}}"
                        title="{{ $website->topTrafficCountryName }}">
                      <div class="flex text-sm text-gray-800 dark:text-gray-400 line-clamp-1"
                        title="{{ $website->topTrafficCountryName }}">
                        {{ $website->topTrafficCountryCode }}
                      </div>
                      @else
                      <span class="text-gray-300">No Data</span>
                      @endif
                    </div>
                  </td>


                  {{-- REFFERING DOMAINS --}}
                  <td class="reffering-domain">
                    <div class="px-6 py-4 text-right">
                      <span class="text-sm  text-gray-7700 dark:text-gray-400">
                        {{ Number::format($website->reffering_domains_count) }}
                      </span>
                    </div>
                  </td>



                  {{-- OUTGOING DOMAINS COUNT --}}
                  <td class="reffering-domain">
                    <div class="px-6 py-4 text-right">
                      <span class="text-sm  text-gray-7700 dark:text-gray-400">

                        @if($website->outgoing_links_count)
                        {{ Number::format($website->outgoing_links_count) }}
                        @else
                        <span class="text-gray-300">No Data</span>
                        @endif
                      </span>
                    </div>
                  </td>



                  {{-- LANGUAGE --}}
                  <td class="language">
                    <div class="px-6 py-4">
                      <span class="text-sm text-gray-700 dark:text-gray-400">
                        {{ $website->languageName }}
                      </span>
                    </div>
                  </td>


                  {{-- SPAM SCORE --}}
                  <td class="quality-score">
                    <div class="px-6 py-4">
                      <div class="flex items-center text-sm">
                        @if($website->moz_spam_score)
                        @if($website->moz_spam_score <= 33) <div
                          class="inline-block w-3 h-3 mr-2 bg-green-500 rounded-full">
                      </div>
                      @elseif($website->moz_spam_score > 33
                      &&
                      $website->moz_spam_score < 66) <div class="inline-block w-3 h-3 mr-2 bg-orange-400 rounded-full">
                    </div>
                    @elseif($website->moz_spam_score >= 66)
                    <div class="inline-block w-3 h-3 mr-2 bg-red-500 rounded-full"></div>
                    @else
                    <div class="inline-block w-3 h-3 mr-2 bg-gray-300 rounded-full"></div>
                    @endif

                    <span class="text-gray-800">
                      {{$website->moz_spam_score == -1 ? 1 :
                      $website->moz_spam_score}}
                    </span>
                    @endif
          </div>
        </div>
        </td>
        {{--
        <td class="rating">
          <div class="px-6 py-4 flex gap-x-1">

            @php
            $rating = round($website->ahref_domain_rank/20)
            @endphp

            @for($i = 0; $i < $rating; $i++) <svg class="w-3 h-3 text-gray-600 dark:text-gray-800"
              xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" viewBox="0 0 16 16">
              <path
                d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z" />
              </svg>
              @endfor

              @for($i = 0; $i < 5 - $rating; $i++) <svg class="w-3 h-3 text-gray-300 dark:text-gray-800"
                xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" viewBox="0 0 16 16">
                <path
                  d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z" />
                </svg>
                @endfor
          </div>
        </td> --}}

        {{-- <td class="value">
          <div class="px-6 py-3">
            <div class="flex items-center gap-x-3">
              <span class="text-xs text-gray-800">
                {{round($website->ahref_domain_rank/20)}}/5
              </span>
              <div class="flex w-12 h-1.5 bg-gray-200 rounded-full overflow-hidden dark:bg-gray-700">
                <div class="flex flex-col justify-center overflow-hidden bg-gray-600 dark:bg-gray-200"
                  style="width:  {{round($website->ahref_domain_rank)}}%" role="progressbar"></div>
              </div>
            </div>
          </div>
        </td>
        --}}



        {{-- LINK RELATION --}}
        <td class="p-4 align-middle pr-0">

          @if($website->link_relation == 'dofollow')
          <div
            class="inline-flex items-center border border-green-700 rounded-full px-2.5 py-[2px] text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 text-green-700 capitalize">
            Dofollow
          </div>
          @elseif($website->link_relation == 'nofollow')
          <div
            class="inline-flex items-center border border-orange-700 rounded-full px-2.5 py-[2px] text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 text-orange-700">
            Nofollow</div>
          @elseif( str_contains($website->link_relation, 'sponsor') )
          <div
            class="inline-flex items-center border border-red-600 rounded-full px-2.5 py-[2px] text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 text-red-600">
            Sponsored</div>
          @else
          <div
            class="inline-flex items-center border border-gay-800 rounded-full px-2.5 py-[2px] text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 text-gray-800">
            {{$website->link_relation}}
          </div>
          @endif

        </td>



        {{-- SPONSORSHIP LABEL --}}
        <td class="td-status">
          <div class="px-6 py-4">

            @if($website->sponsorship_label)
            <span
              class="inline-flex items-center gap-1.5 py-0.5 px-2 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-green-200">
              <x-icons.etc.cancel />
              Yes
            </span>
            @else
            <span
              class="inline-flex items-center gap-1.5 py-0.5 px-2 rounded-full text-xs font-medium bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-green-200">
              <span class="w-3 h-3">
                <x-icons.lucide.check-circle />
              </span>
              No
            </span>
            @endif
          </div>
        </td>


        {{-- CATEGORY --}}
        <td class="category">
          <a target="_blank" class="" href="#">
            <div class="px-4 py-4">
              <span
                class="whitespace-nowrap  capitalize inline-flex items-center gap-1.5 px-3 py-2 rounded-full text-sm text-gray-800 dark:bg-gray-900 dark:text-gray-200">
                @if(isset($website->category))
                {{$website->category}}
                @endif
              </span>
            </div>
          </a>
        </td>



        {{-- DOMAIN AGE --}}
        <td class="age">
          <div class="px-4 py-4">
            <span
              class="whitespace-nowrap capitalize inline-flex items-center gap-1.5 px-3 py-2 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200">

              @if($website->domain_registration_date)
              {{-- helper function --}}
              {{ m_calculateAge($website->domain_registration_date) }}
              @else
              <span class="text-gray-300">No Data</span>
              @endif
            </span>
          </div>
        </td>

        {{-- <td class="action-options-website">
          <div class="px-6 py-4">
            <div x-data="{display: false}" class="hs-dropdown relative inline-block [--placement:bottom-right]">

              <button x-on:click="display =! display" id="hs-table-dropdown-1" type="button"
                class="hs-dropdown-toggle py-1.5 px-2 inline-flex justify-center items-center gap-2 text-gray-800 align-middle focus:ring-2 focus:ring-offset-2 focus:ring-gray-200 transition-all text-sm dark:text-gray-400 dark:hover:text-white dark:focus:ring-offset-gray-400 focus:bg-gray-100 hover:bg-gray-100 rounded-md">
                <svg class="w-4 h-4" xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor"
                  viewBox="0 0 16 16">
                  <path
                    d="M3 9.5a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3zm5 0a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3zm5 0a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3z" />
                </svg>
              </button>

              <div @click.away="display=false" x-transition:enter="ease-out duration-200"
                x-transition:enter-start="-translate-y-2" x-transition:enter-end="translate-y-0" x-show="display"
                x-cloack
                class="hs-dropdown-menu transition-[opacity,margin] duration hs-dropdown-open:opacity-100 divide-y divide-gray-200 min-w-[12rem] z-20 bg-white shadow-2xl py-2 px-2 mt-2 right-0 dark:divide-gray-700 dark:bg-gray-800 dark:border dark:border-gray-700 absolute border rounded-md  border-neutral-200/70 text-neutral-700"
                aria-labelledby="hs-table-dropdown-1">

                <div class="py-2 first:pt-0 last:pb-0">
                  <span class="block py-2 px-2 text-xs font-medium uppercase text-gray-400 dark:text-gray-800">
                    Actions
                  </span>
                  <a target="_blank"
                    class="flex items-center gap-x-3 py-2 px-3 rounded-md font-medium text-sm hover:bg-gray-100 hover:text-gray-800 focus:ring-2 focus:ring-gray-500 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300"
                    href="#">
                    <x-icons.iconly.cart />
                    Add To Cart
                  </a>
                  <a target="_blank"
                    class="flex items-center gap-x-3 py-2 px-3 rounded-md font-medium text-sm hover:bg-gray-100 hover:text-gray-800 focus:ring-2 focus:ring-gray-500 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-300"
                    href="#">
                    <x-icons.iconly.star />
                    Add To Favorites
                  </a>
                </div>
                <div class="py-2 first:pt-0 last:pb-0">
                  <a target="_blank"
                    class="flex items-center gap-x-3 py-2 px-3 rounded-md font-medium text-sm text-red-600 hover:bg-gray-100 hover:text-red-700 focus:ring-2 focus:ring-gray-500 dark:text-red-500 dark:hover:bg-gray-700 dark:hover:text-gray-300"
                    href="#">
                    <x-icons.iconly.flag />
                    Report Site
                  </a>
                </div>
              </div>
            </div>
          </div>
        </td> --}}

        </tr>



        {{-- ***************************************************** --}}
        {{-- INFO ROW --}}
        {{-- ***************************************************** --}}
        <tr x-cloak x-data="{id: {{ $website->id }} }" x-show="id == activeInfo" key="info-{{ $website->id }}">

          <td colspan="18" class="p-4 py-6">
            <div class="w-full flex">


              <div class="right-data w-full border-2 rounded-lg">

                <div class="top-data p-4 space-x-3 flex items-center bg-gray-100/80 rounded-t-lg">

                  <div>
                    <img width="32px" height="32px"
                      src="https://www.google.com/s2/favicons?sz=64&domain_url={{ $website->website_domain}}"
                      class="w-14 h-14 p-3 bg-white border-2 rounded-full m-2">
                  </div>

                  <div class="flex flex-col space-y-1">

                    @if($website->site_title)
                    <a href="//{{ $website->website_domain}}" target="_blank"
                      class="site-title font-bold text-sm text-gray-600 flex items-center">
                      <span class="capitalize max-w-2xl line-clamp-1">
                        {{ $website->site_title}}
                      </span>
                      <span class="w-3 h-3 ml-0.5 mb-1 font-bold">
                        <x-icons.lucide.arrow-up-right />
                      </span>
                    </a>
                    @endif

                    @if($website->site_description)
                    <div class="site-description text-sm leading-5 text-gray-500 max-w-2xl line-clamp-2">
                      {{ $website->site_description }}
                    </div>
                    @endif

                    <a target="_blank" href="//{{ $website->website_domain}}"
                      class="site-title font-medium text-xs text-gray-500 lowercase">
                      {{ $website->website_domain}}
                    </a>
                  </div>
                </div>


                {{-- *********** --}}
                {{-- SUB-COLUMNS --}}
                {{-- *********** --}}
                <div
                  class="secondary-data grid grid-cols-6 text-sm p-4 pt-1 space-x-4 bg-gray-100/80 justify-between rounded-b-lg">


                  {{-- Engagment --}}
                  <div class="engagment-scores p-4 rounded border border-gray-200 bg-white">
                    <div class="font-bold mb-3 text-gray-600">Engagment</div>
                    <div class="divide-y-2 divide-gray-100">

                      <div class="flex justify-between py-2.5">
                        <div class="text-gray-700/90 flex items-center">
                          <span class="w-4 h-4 mr-1">
                            <x-icons.lucide.bounce />
                          </span>
                          <span>Bounce Rate:</span>
                        </div>
                        <div class="text-gray-700">
                          {{ $website->bounce_rate ?? 'No Data'}}%
                        </div>
                      </div>

                      <div class="flex justify-between py-2.5">
                        <div class="text-gray-700/90 flex items-center">
                          <span class="w-4 h-4 mr-1">
                            <x-icons.lucide.site />
                          </span>
                          <span>Pages Per Visit:</span>
                        </div>
                        <div class="text-gray-700">
                          {{ $website->pages_per_visit ?? 'No Data' }}
                        </div>
                      </div>

                      <div class="flex justify-between py-2.5">
                        <div class="text-gray-700/90 flex items-center">
                          <span class="w-4 h-4 mr-1">
                            <x-icons.lucide.clock />
                          </span>
                          <span>Avg Time on Site:</span>
                        </div>
                        <div class="text-gray-700">
                          {{ (m_timeDisplay($website->avg_time_on_site)) ?? 'No Data' }}
                        </div>
                      </div>

                      <div class="flex justify-between py-2.5">
                        <div class="text-gray-700/90 flex items-center">
                          <span class="w-4 h-4 mr-1">
                            <x-icons.lucide.chart-combined />
                          </span>
                          <span>Total Monthly Traffic:</span>
                        </div>
                        <div class="text-gray-700">
                          {{ Number::abbreviate(($website->similarweb_traffic ?? $website->ahref_organic_traffic), 1) }}
                        </div>
                      </div>

                    </div>
                  </div>



                  {{-- ************* --}}
                  {{-- TOP COUNTRIES --}}
                  <div class="top-countries p-4 rounded border border-gray-200 bg-white">
                    <div class="font-bold mb-3 text-gray-600">Top Traffic Countries</div>


                    @if($website->similarweb_top_countries)

                    <div class="text-gray-700 divide-y-2 divide-gray-100">

                      @php
                      $country = json_decode($website->similarweb_top_countries, true);
                      @endphp

                      <div class="flex justify-between py-1.5">
                        <div class="flex">
                          <img class="flex w-4 h-4 mr-1.5  border-gray-100 
                                                 border object-contain"
                            src="{{asset('storage/graphics/country-flags/'. $country['CountryCode'] .'.svg');}}">
                          <span>{{ $country['CountryCode'] }} </span>
                        </div>
                        <div>{{ $country['Rank'] }}%</div>
                      </div>
                    </div>

                    @else
                    <div class="flex h-4/5 items-center justify-center content-center self-center">
                      <div>
                        <x-icons.etc.drawer />
                        <div class="text-gray-200 text-center">Data Not Available</div>
                      </div>
                    </div>
                    @endif
                  </div>



                  {{-- *************** --}}
                  {{-- TRAFFIC SOURCES --}}
                  <div class="traffic-sources p-4 rounded border border-gray-200 bg-white">
                    <div class="font-bold mb-3 text-gray-600">Traffic Sources</div>
                    @if($website->similarweb_traffic_sources_percentage)

                    @php
                    $trafifcSources = json_decode($website->similarweb_traffic_sources_percentage, true);
                    @endphp

                    <div class="text-gray-700 divide-y-2 divide-gray-100">

                      <div class="flex justify-between py-1">
                        <div class="flex items-center">
                          <span class="h-2 w-2 flex rounded-full bg-red-600/80 mr-1.5"></span>
                          <span class="text-gray-700/90">Search</span>
                        </div>
                        <div class="text-gray-700">
                          {{ $trafifcSources['Search'] ?? '<1' }}% </div>
                        </div>

                        <div class="flex justify-between py-1">
                          <div class="flex items-center">
                            <span class="h-2 w-2 flex rounded-full bg-green-600/80 mr-1.5"></span>
                            <span class="text-gray-700/90">Direct</span>
                          </div>
                          <div class="text-gray-700">
                            {{ $trafifcSources['Direct'] ?? '<1' }}% </div>
                          </div>

                          <div class="flex justify-between py-1">
                            <div class="flex items-center">
                              <span class="h-2 w-2 flex rounded-full bg-blue-600/80 mr-1.5"></span>
                              <span class="text-gray-700/90">Social</span>
                            </div>
                            <div class="text-gray-700">
                              {{ $trafifcSources['Social'] ?? '<1' }}% </div>
                            </div>

                            <div class="flex justify-between py-1">
                              <div class="flex items-center">
                                <span class="h-2 w-2 flex rounded-full bg-amber-600/80 mr-1.5"></span>
                                <span class="text-gray-700/90">Search</span>
                              </div>
                              <div class="text-gray-700">
                                {{ $trafifcSources['Mail'] ?? '<1' }}% </div>
                              </div>

                              <div class="flex justify-between py-1">
                                <div class="flex items-center">
                                  <span class="h-2 w-2 flex rounded-full bg-purple-600/80 mr-1.5"></span>
                                  <span class="text-gray-700/90">Referrals</span>
                                </div>
                                <div class="text-gray-700">
                                  {{ $trafifcSources['Referrals'] ?? '<1' }}% </div>
                                </div>

                                <div class="flex justify-between py-1">
                                  <div class="flex items-center">
                                    <span class="h-2 w-2 flex rounded-full bg-fuchsia-600/80 mr-1.5"></span>
                                    <span class="text-gray-700/90">Paid</span>
                                  </div>
                                  <div class="text-gray-700">
                                    {{ $trafifcSources['Paid Referrals'] ?? '<1' }}% </div>
                                  </div>

                                </div>
                                @else
                                <div class="flex h-4/5 items-center justify-center content-center self-center">
                                  <div>
                                    <x-icons.etc.drawer />
                                    <div class="text-gray-200 text-center">Data Not Available</div>
                                  </div>
                                </div>
                                @endif
                              </div>


                              {{-- ************* --}}
                              {{-- KEYWORDS LIST --}}
                              <div class="website_keywords p-4 rounded border border-gray-200 bg-white">
                                <div class="font-bold mb-3 text-gray-600">Top Ranking Keywords</div>

                                @if(count($website->keyword_website) < 1) <div
                                  class="flex h-4/5 items-center justify-center content-center self-center">
                                  <div>
                                    <x-icons.etc.drawer />
                                    <div class="text-gray-200 text-center">Data Not Available</div>
                                  </div>
                              </div>
                              @else

                              <div class="divide-y-2 divide-gray-100 text-gray-700 py-1.5">
                                @foreach($website->keyword_website as $keyword)
                                <div class="flex py-1 items-center">
                                  <div class="w-4 h-4 mr-1 text-gray-400">
                                    <x-icons.lucide.arrow-right />
                                  </div>
                                  <div class="line-clamp-1 ">
                                    {{ $keyword->name }}
                                  </div>
                                </div>
                                @if($loop->iteration > 5)
                                @break
                                @endif
                                @endforeach
                              </div>

                              @endif

                            </div>



                            {{-- *********** --}}
                            {{-- TOPICS LIST --}}
                            <div class="topics-list p-4 rounded border border-gray-200 bg-white">
                              <div class="font-bold mb-3 text-gray-600">Website Topics</div>
                              <div class="flex flex-wrap divide-y-2 divide-gray-100 text-gray-600">
                                @foreach($website->topics as $topic)
                                <div
                                  class="flex grow justify-center py-1 px-2 my-1.5 mx-1 text-sm bg-gray-100/90 rounded-full capitalize">
                                  {{ $topic->name }}
                                </div>
                                @if($loop->iteration > 10)
                                @break
                                @endif
                                @endforeach
                              </div>
                            </div>



                            {{-- *************** --}}
                            {{-- PUBLISHING LIST --}}
                            <div class="Publish-data p-4 rounded border border-gray-200 bg-white">
                              <div class="font-bold mb-3 text-gray-600">Publishing</div>

                              <div class=" divide-y-2 divide-gray-100 text-gray-600">

                                <div class="flex justify-between py-3">
                                  <div class="w-1/2 font-semibold text-gray-500">Sample Post: </div>
                                  <div class="line-clamp-1 w-1/2">
                                    @if(!empty($website->example_post_url))
                                    <a href="{{ $website->example_post_url }}">
                                      {{ $website->example_post_url }}
                                    </a>
                                    @else
                                    Check Site For Sample Content
                                    @endif
                                  </div>
                                </div>

                                <div class="flex py-3">
                                  <div class="font-semibold text-gray-500 w-1/2">Verified Publisher</div>
                                  <div class="w-1/2">No</div>
                                </div>

                                @if(!empty($website->site_requirements))
                                <div class="py-3">
                                  <div class="font-semibold text-gray-500 pb-3">Requirements</div>
                                  <div class="max-h-20 overflow-scroll text-xs">{{$website->site_requirements ?? 'Check
                                    Site' }}</div>
                                </div>
                                @endif

                              </div>
                            </div>


                          </div>
                        </div>
                      </div>
          </td>
        </tr>



        @endforeach




        @if($websites->total() < 1) <tr>
          <td colspan="12">
            <x-examples.preline.empty-state />
          </td>
          </tr>
          @endif







          </tbody>

          </table>

      </div>
      <!-- End Table -->





      {{-- <div class="pagination mt-6 mb-2 mx-6">
        {{ $websites->links() }}
      </div> --}}


      <div id="pagination-table" class="pagination mt-4  px-6 pt-6 pb-4 border-t border-gray-200 dark:border-gray-700">
        {{ $websites->onEachSide(1)->links('components.marketplace.table.pagination-alpine-morph') }}
      </div>



      <!-- Pagination second Footer -->
      {{-- <div
        class="px-6 py-4 grid gap-3 md:flex md:justify-between md:items-center border-t border-gray-200 dark:border-gray-700">


        <div class="inline-flex items-center gap-x-2">
          <p class="text-sm text-gray-800 dark:text-gray-400">
            Showing:
          </p>
          <div class="max-w-sm space-y-3">
            <select
              class="py-2 px-3 pr-9 block w-full border-gray-200 rounded-md text-sm focus:border-gray-500 focus:ring-gray-500 dark:bg-slate-900 dark:border-gray-700 dark:text-gray-400">
              <option>1</option>
              <option>2</option>
              <option>3</option>
              <option>4</option>
              <option selected>9</option>
              <option>20</option>
            </select>
          </div>
          <p class="text-sm text-gray-800 dark:text-gray-400">
            of 20
          </p>
        </div>

        <nav>
          <ul
            class="flex items-center text-sm leading-tight bg-white border divide-x rounded h-9 text-neutral-500 divide-neutral-200">
            <li class="h-full">
              <a target="_blank" href="#"
                class="relative inline-flex items-center h-full px-3 ml-0 rounded-l group hover:text-neutral-900">
                <span>Previous</span>
              </a>
            </li>
            <li class="hidden h-full md:block">
              <a target="_blank" href="#"
                class="relative inline-flex items-center h-full px-3 text-neutral-900 group bg-gray-50">
                <span>1</span>
                <span
                  class="box-content absolute bottom-0 left-0 w-full h-px -mx-px translate-y-px border-l border-r bg-neutral-900 border-neutral-900"></span>
              </a>
            </li>
            <li class="hidden h-full md:block">
              <a target="_blank" href="#"
                class="relative inline-flex items-center h-full px-3 group hover:text-neutral-900">
                <span>2</span>
                <span
                  class="box-content absolute bottom-0 w-0 h-px -mx-px duration-200 ease-out translate-y-px border-transparent bg-neutral-900 group-hover:border-l group-hover:border-r group-hover:border-neutral-900 left-1/2 group-hover:left-0 group-hover:w-full"></span>
              </a>
            </li>
            <li class="hidden h-full md:block">
              <a target="_blank" href="#"
                class="relative inline-flex items-center h-full px-3 group hover:text-neutral-900">
                <span>3</span>
                <span
                  class="box-content absolute bottom-0 w-0 h-px -mx-px duration-200 ease-out translate-y-px border-transparent bg-neutral-900 group-hover:border-l group-hover:border-r group-hover:border-neutral-900 left-1/2 group-hover:left-0 group-hover:w-full"></span>
              </a>
            </li>
            <li class="hidden h-full md:block">
              <div class="relative inline-flex items-center h-full px-2.5 group">
                <span>...</span>
              </div>
            </li>
            <li class="hidden h-full md:block">
              <a target="_blank" href="#"
                class="relative inline-flex items-center h-full px-3 group hover:text-neutral-900">
                <span>6</span>
                <span
                  class="box-content absolute bottom-0 w-0 h-px -mx-px duration-200 ease-out translate-y-px border-transparent bg-neutral-900 group-hover:border-l group-hover:border-r group-hover:border-neutral-900 left-1/2 group-hover:left-0 group-hover:w-full"></span>
              </a>
            </li>
            <li class="hidden h-full md:block">
              <a target="_blank" href="#"
                class="relative inline-flex items-center h-full px-3 group hover:text-neutral-900">
                <span>7</span>
                <span
                  class="box-content absolute bottom-0 w-0 h-px -mx-px duration-200 ease-out translate-y-px border-transparent bg-neutral-900 group-hover:border-l group-hover:border-r group-hover:border-neutral-900 left-1/2 group-hover:left-0 group-hover:w-full"></span>
              </a>
            </li>
            <li class="hidden h-full md:block">
              <a target="_blank" href="#"
                class="relative inline-flex items-center h-full px-3 group hover:text-neutral-900">
                <span>8</span>
                <span
                  class="box-content absolute bottom-0 w-0 h-px -mx-px duration-200 ease-out translate-y-px border-transparent bg-neutral-900 group-hover:border-l group-hover:border-r group-hover:border-neutral-900 left-1/2 group-hover:left-0 group-hover:w-full"></span>
              </a>
            </li>
            <li class="h-full">
              <a target="_blank" href="#"
                class="relative inline-flex items-center h-full px-3 rounded-r group hover:text-neutral-900">
                <span>Next</span>
              </a>
            </li>
          </ul>
        </nav>
      </div> --}}


      <!-- End Footer -->

    </div>
  </div>
</div>
</div>
<!-- End Card -->
</div>
<!-- End Table Section -->














<style>
  td.domain {
    padding-right: 20px;
    text-transform: lowercase;
  }

  /*td.buy-button{
  padding-right:30px;
}*/

  td.price {
    padding-left: 15px;
  }

  /*td.buy-quantity{
  padding-left:15px;
}*/
</style>