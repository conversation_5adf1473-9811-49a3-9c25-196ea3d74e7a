<?php

namespace App\Models;


use App\Enums\Role;
use App\Enums\MarketplaceOrderStatus;
use Illuminate\Support\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\Observers\MarketplaceOrderObserver;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;



/*********************************************************************
 * MARKETPLACE ORDER MODEL
 *********************************************************************
 *
 * Represents an order in the marketplace system.
 * Handles order processing, payment, and status management.
 *
 * - Manages order creation and payment processing
 * - Handles wallet transactions for orders
 * - Provides order status and relationship management
 *
 *********************************************************************/
#[ObservedBy([MarketplaceOrderObserver::class])]
class MarketplaceOrder extends Model
{
    use HasFactory;

    /**
     * 
     * @property int $id
     * @property int $user_id
     * @property int $payment_id
     * @property float $price_paid
     * @property int $items_in_orders
     * @property string $status
     * @property \Carbon\Carbon $created_at
     * @property \Carbon\Carbon $updated_at
     * 
     * @property-read \App\Models\User $user
     * @property-read \App\Models\MarketplacePayment $payment
     * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\MarketplaceSingleOrderItem $orderItems
     */

    // TABLE
    // -------------------------------------------------------------------------------- //
    protected $table = 'marketplace_orders';

    // GUARDED
    // -------------------------------------------------------------------------------- //
    protected $guarded  = [];

    // APPENDS
    // -------------------------------------------------------------------------------- //
    protected $appends = ['created_at_formatted', 'updated_at_formatted', 'time_elapsed', 'payment_method'];



    // FILLABLE
    // -------------------------------------------------------------------------------- //
    protected $fillable = [
        'user_id',
        'payment_id',
        'price_paid',
        'items_in_orders',
        'status',
    ];


    // SORTING FIELDS
    // -------------------------------------------------------------------------------- //
    protected array $sortable = [
        'id',
        'created_at',
        'updated_at',
        'price_paid',
        'items_in_orders',
    ];



    // BOOT
    // -------------------------------------------------------------------------------- //
    public static function boot()
    {
        parent::boot();

        // Add global scope for user-based filtering
        static::addGlobalScope('user_orders', function ($builder) {
            // -----------------------
            // Apply User-Based Order Filtering
            if (Auth::check() && Auth::user()->role === Role::Advertiser->value) {
                $builder->where('user_id', Auth::id());
            } elseif (Auth::check() && Auth::user()->role === Role::Publisher->value) {
                $builder->whereHas('orderItems.website.publisher', function ($query) {
                    $query->where('id', Auth::id());
                });
            }
        });
    }



    /*********************************************************************
     * RELATIONSHIPS
     *********************************************************************
     *
     * Define all model relationships.
     * Includes payment, user, and order items.
     *
     * @return \Illuminate\Database\Eloquent\Relations\Relation
     *
     *********************************************************************/
    public function payment(): HasOne
    {
        return $this->hasOne(MarketplacePayment::class, 'id', 'payment_id');
    }





    /*********************************************************************
     * USER
     *********************************************************************
     * 
     * Get the user for the order.
     * 
     *********************************************************************/
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }


    /*********************************************************************
     * ORDER ITEMS
     *********************************************************************
     * 
     * Get the order items for the order.
     * 
     *********************************************************************/
    public function orderItems(): HasMany
    {
        return $this->hasMany(MarketplaceSingleOrderItem::class, 'order_id');
    }




    /*********************************************************************
     * ATTRIBUTES
     *********************************************************************
     *
     * Define accessor methods for various order attributes.
     * Includes formatted dates and payment information.
     *
     * @return mixed
     *
     *********************************************************************/
    public function getCreatedAtFormattedAttribute(): string
    {
        return Carbon::parse($this->created_at)->format('d M, Y');
    }




    /*********************************************************************
     * UPDATED AT FORMATTED
     *********************************************************************
     * 
     * Get the formatted updated at date.
     * 
     *********************************************************************/
    public function getUpdatedAtFormattedAttribute(): string
    {
        return Carbon::parse($this->updated_at)->format('d M, Y');
    }




    /*********************************************************************
     * TIME ELAPSED
     *********************************************************************
     * 
     * Get the time elapsed since the order was created.
     * 
     *********************************************************************/
    public function getTimeElapsedAttribute(): string
    {
        return $this->created_at ? $this->created_at->diffForHumans() : 'N/A';
    }




    /*********************************************************************
     * PAYMENT METHOD
     *********************************************************************
     * 
     * Get the payment method for the order.
     * 
     *********************************************************************/
    public function getPaymentMethodAttribute(): string
    {
        return env('PAYMENT_METHOD', 'stripe');
    }


    /**********************************
     * SCOPE ORDER COMPLETED
     ***********************************/
    public function scopeOrderCompleted($query)
    {
        return $query->where('status', MarketplaceOrderStatus::COMPLETED->value);
    }

    /**********************************
     * SCOPE ORDER PENDING
     ***********************************/
    public function scopeOrderPending($query)
    {
        return $query->where('status', MarketplaceOrderStatus::PENDING->value);
    }


    /**********************************
     * SCOPE ORDER IN PROGRESS
     ***********************************/
    public function scopeOrderInProgress($query)
    {
        return $query->where('status', MarketplaceOrderStatus::IN_PROGRESS->value);
    }


    /**********************************
     * SCOPE ORDER LATE
     ***********************************/
    public function scopeOrderLate($query)
    {
        return $query->where('status', MarketplaceOrderStatus::LATE->value);
    }


    /******************************
     * SCOPE STATUS
    ******************************/
    public function scopeStatus($query, string $status)
    {
        return $query->where('status', $status);
    }


    /******************************
     * SCOPE SEARCH
    ******************************/
    public function scopeSearch($query, ?string $search)
    {
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('id', 'like', '%' . $search . '%')
                    ->orWhereHas('user', function ($q2) use ($search) {
                        $q2->where('name', 'like', '%' . $search . '%')
                            ->orWhere('email', 'like', '%' . $search . '%');
                    })
                    ->orWhere('price_paid', 'like', '%' . $search . '%');
            });
        }

        return $query;
    }


    /******************************
     * SCOPE SORT
    ******************************/
    public function scopeSort($query ,$sortField = 'id', $sortOrder = 'desc'){

        if (in_array($sortField, $this->sortable)) {
            $query->orderBy($sortField, $sortOrder);
        }
        return $query;
    }
}
