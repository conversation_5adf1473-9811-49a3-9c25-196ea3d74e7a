<?php

namespace App\Models\Traits;

use App\Enums\OrderItemStates;


trait Badgeable
{

    public function getBadgeColorAttribute()
    {
        switch ($this->state_name) {
            case OrderItemStates::OrderItemCompleted->value:
                return 'green';
            case OrderItemStates::OrderItemCancelled->value:
                return 'red';
            default:
                return 'orange';
        }
    }


    public function getBadgeIconAttribute()
    {
        switch ($this->state_name) {
            case OrderItemStates::OrderItemCompleted->value:
                return 'check-circle';
            case OrderItemStates::OrderItemCancelled->value:
                return 'x-circle';
            default:
                return 'triangle-alert';
        }
    }
}
