<?php

namespace App\Models\Observers;

use App\Models\MarketplaceOrder;
use Illuminate\Support\Facades\App;
use Bavix\Wallet\Services\AtomicServiceInterface;

/*********************************************************************
 * MARKETPLACE ORDER OBSERVER CLASS
 *********************************************************************
 *
 * Provides a clean way to organize model observers with separate methods
 * ..for each observer type. This allows for better code organization and
 * ..easier testing of individual observer logic.
 *
 * - Registers global scopes for user-based order filtering
 * - Handles order creation validation and wallet operations
 * - Manages checkout session updates and wallet credits
 *
 *********************************************************************/
class MarketplaceOrderObserver
{



    /*******************************************************************
     * REGISTER CREATING OBSERVERS
     *********************************************************************
     *
     * Validates order creation by ensuring super admin user exists
     * ..before allowing order creation.
     *
     * @return void
     *
     *******************************************************************/
    public static function creating(MarketplaceOrder $order): void
    {
        // -----------------------
        // Validate Super Admin Exists
        if (super_admin_user() == null) {
            throw new \Exception('Cannot Create Order, Super Admin User Not Found.');
        }
    }


    /*******************************************************************
     * REGISTER CREATED OBSERVERS
     *********************************************************************
     *
     * Handles post-order creation actions including checkout success
     * ..processing and wallet credit operations.
     *
     * @return void
     *
     *******************************************************************/
    public static function created(MarketplaceOrder $order): void
    {
        // -----------------------
        // Process Order Creation Actions
        self::creditWallets($order);
    }



    /*******************************************************************
     * CREDIT WALLETS FOR ORDER
     *********************************************************************
     *
     * Credits user wallet and transfers funds to admin wallet using
     * ..atomic operations to ensure data consistency.
     *
     * @param MarketplaceOrder $order
     * The order for which wallets should be credited
     *
     * @return void
     *
     *******************************************************************/
    public static function creditWallets(MarketplaceOrder $order)
    {
        // -----------------------
        // Initialize Variables
        $user = $order->user;
        $admin = super_admin_user();
        $amount = $order->price_paid * 100; // Convert to cents
        $atomicService = App::make(AtomicServiceInterface::class);


        // -----------------------
        // Perform Atomic Wallet Operations
        $atomicService->blocks(
            [$user->wallet, $admin?->wallet],
            function () use ($user, $amount, $order, $admin) {
                // -----------------------
                // Credit User Wallet
                $user->wallet->deposit($amount, [
                    'order_id'  => $order->id,
                    'reference' => 'Order Payment Credit By Credit Card',
                    'type'      => 'order_payment_credit',
                    'message'   => 'Order payment credited to your wallet',
                ]);


                // -----------------------
                // Transfer to Admin Wallet
                if ($admin) {
                    $user->wallet->transfer($admin->wallet, $amount, [
                        'order_id'  => $order->id,
                        'reference' => 'Order Payment Transfer To Admin Wallet',
                        'type'      => 'order_payment_transfer',
                        'message'   => 'Order payment transferred from user to admin',
                    ]);
                }
            }
        );
    }
}
