<?php

namespace App\Models;

use App\Enums\OutreachStatus;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Outreach extends Model
{
    use HasFactory;

    /**
     *
     * @property int $id
     * @property int $marketplace_website_id
     * @property int $user_id
     * @property string $status
     * @property string $notes
     * @property \Carbon\Carbon $onboarded_at
     *
     * @property-read \App\Models\MarketplaceWebsite $website
     * @property-read \App\Models\User $user
     */

    protected $fillable = [
        'marketplace_website_id',
        'user_id',
        'status',
        'notes',
        'onboarded_at'
    ];

    protected $casts = [
        'onboarded_at' => 'datetime',
    ];

    // RELATIONS
    // ----------------------------------------------------------------------------

    public function website()
    {
        return $this->belongsTo(MarketplaceWebsite::class, 'marketplace_website_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // SCOPES
    // ----------------------------------------------------------------------------

    /**
     * Scope for filtering by status (supports both enum and string)
     */
    public function scopeWithStatus(Builder $query, OutreachStatus|string $status): Builder
    {
        $statusValue = $status instanceof OutreachStatus ? $status->value : $status;
        return $query->where('status', $statusValue);
    }

    /**
     * Scope for in-progress outreaches
     */
    public function scopeInProgress(Builder $query): Builder
    {
        return $query->withStatus(OutreachStatus::IN_PROGRESS);
    }

    /**
     * Scope for onboarded outreaches
     */
    public function scopeOnboarded(Builder $query): Builder
    {
        return $query->withStatus(OutreachStatus::ONBOARDED);
    }

    /**
     * Scope for rejected outreaches
     */
    public function scopeRejected(Builder $query): Builder
    {
        return $query->withStatus(OutreachStatus::REJECTED);
    }

    /**
     * Scope for recent outreaches (last 30 days)
     */
    public function scopeRecent(Builder $query): Builder
    {
        return $query->where('created_at', '>=', now()->subDays(30));
    }

    /**
     * Scope for outreaches by user
     */
    public function scopeByUser(Builder $query, int $userId): Builder
    {
        return $query->where('user_id', $userId);
    }
}
