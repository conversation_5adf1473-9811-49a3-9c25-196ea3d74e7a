<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MarketplaceCheckoutSession extends Model
{
    use HasFactory;

    /**
     * 
     * @property int $id
     * @property int $user_id
     * @property array $cart_items_ids
     * @property float $total_amount
     * @property int $total_items
     * @property bool $checkout_successful
     * @property string $stripe_payment_intent
     * @property string $checkout_ip_address
     * @property string $order_memo
     * @property \Carbon\Carbon $created_at
     * @property \Carbon\Carbon $updated_at
     * 
     * @property-read \App\Models\User $user
     * @property-read \App\Models\MarketplaceOrder $order
     */

    protected $guarded = [];

    protected $casts = [
        'cart_items_ids' => 'array',
    ];

    protected $fillable = [
        'user_id',
        'cart_items_ids',
        'total_amount',
        'total_items',
        'order_memo',
        'checkout_ip_address',
        'stripe_payment_intent',
    ];

    public function order()
    {
        return $this->hasOne(MarketplaceOrder::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
