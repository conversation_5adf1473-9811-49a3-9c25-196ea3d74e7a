<?php

namespace App\Models;

use App\Enums\Niche;
use App\Jobs\FetchSeoStatsForWebsite;
use App\Models\Traits\AcceptsNiche;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;


/*********************************************************************
 * MARKETPLACE WEBSITE MODEL
 *********************************************************************
 *
 * Represents a website in the marketplace system.
 * Handles website properties, relationships, and business logic.
 *
 * - Manages website details and pricing
 * - Handles relationships with publishers, orders, and SEO stats
 * - Provides niche-specific functionality
 *
 *********************************************************************/
class MarketplaceWebsite extends Model
{
    use HasFactory, AcceptsNiche;

    /**
     * 
     * @property int $id
     * @property string $website_domain
     * @property int $site_language_id
     * @property int $main_category_id
     * @property int $category_global_rank
     * @property string $site_title
     * @property string $site_description
     * @property string $domain_registration_date
     * @property int $favicon_image_id
     * @property int $screenshot_image_id
     * @property int $initial_price_before_negotiation
     * @property int $profit_share_percentage
     * @property int $guest_post_price
     * @property int $link_insert_price
     * @property int $casino_post_price
     * @property int $adult_post_price
     * @property int $finance_post_price
     * @property int $dating_post_price
     * @property int $cbd_post_price
     * @property int $crypto_post_price
     * @property string $site_requirements
     * @property string $example_post_url
     * @property int $article_validity_in_months
     * @property int $turn_around_time_in_days
     * @property int $indexed_article
     * @property string $link_relation
     * @property int $sponsorship_label
     * @property int $homepage_visible
     * @property int $publisher_user_id
     * @property string $contact_email
     * @property bool $active
     * @property string $site_source
     * @property string $internal_note
     * @property string $created_at
     * @property string $updated_at
     * 
     */

    protected $guarded = [];

    // public $timestamps = true; 


    // Setting our profit margin ratio, 
    // ..that's multiplied with base price
    public const PROFIT_MARGIN_MULTIPLIER_GENERAL = 1.2;
    public const PROFIT_MARGIN_MULTIPLIER_CASINO = 1.3;
    public const PROFIT_MARGIN_MULTIPLIER_ADULT = 1.4;
    public const PROFIT_MARGIN_MULTIPLIER_CRYPTO = 1.5;
    public const PROFIT_MARGIN_MULTIPLIER_DATING = 1.5;
    public const PROFIT_MARGIN_MULTIPLIER_CBD = 1.5;
    public const PROFIT_MARGIN_MULTIPLIER_FINANCE = 1.5;
    public const PROFIT_MARGIN_MULTIPLIER_LINK_INSERT = 1.5;
    /**********************************
     * BOOT
     ***********************************/
    public static function boot()
    {
        parent::boot();

        static::created(function ($model) {
            //Dispatch job to fetch seo stats
            FetchSeoStatsForWebsite::dispatch($model)->onQueue('seostats');
        });
    }
    /**********************************
     * RELATIONS
     ***********************************/
    public function seoStats(): HasOne
    {
        return $this->hasOne(MarketplaceWebsiteSeoStat::class, 'marketplace_website_id');
    }

    public function inCart(): HasMany
    {
        return $this->hasMany(MarketplaceCartItem::class, 'marketplace_website_id');
    }

    public function orders(): HasMany
    {
        return $this->hasMany(MarketplaceSingleOrderItem::class);
    }

    public function top_traffic_country(): BelongsTo
    {
        return $this->belongsTo(MarketplaceWebsiteCountry::class, 'top_traffic_country_id');
    }

    public function language(): BelongsTo
    {
        return $this->belongsTo(MarketplaceWebsiteLanguage::class, 'site_language_id');
    }

    public function category(): BelongsTo
    {
        return $this->belongsTo(MarketplaceWebsiteCategory::class, 'main_category_id');
    }

    public function publisher(): BelongsTo
    {
        return $this->belongsTo(User::class, 'publisher_user_id');
    }

    // public function topics(): BelongsToMany
    // {
    //     return $this->belongsToMany(Topic::class, 'topic_website', 'website_id', 'topic_id');
    // }

    public function topics(): BelongsToMany
    {
        return $this->belongsToMany(Topic::class, 'topic_website', 'marketplace_website_id', 'topic_id');
    }

    public function keyword_website(): BelongsToMany
    {
        return $this->belongsToMany(Keyword::class, 'keyword_website', 'website_id', 'keyword_id');
    }



    /**********************************
     * GET PRICE FOR SPECIFIC NICHE
     ***********************************/
    public function nichePrice($niche)
    {
        $priceField = $niche . '_post_price';

        // Special cases for non-standard field names
        if ($niche == Niche::General->value) {
            $priceField = 'guest_post_price';
        } elseif ($niche == Niche::LinkInsert->value) {
            $priceField = 'link_insert_price';
        }

        return $this->{$priceField} ?? 0;
    }


    public function nichePriceOriginal($niche)
    {
        $priceField = $niche . '_post_price';
        $marginConstant = 'PROFIT_MARGIN_MULTIPLIER_' . strtoupper($niche);

        // Special cases for non-standard field names
        if ($niche == Niche::General->value) {
            $priceField = 'guest_post_price';
        } elseif ($niche == Niche::LinkInsert->value) {
            $priceField = 'link_insert_price';
        }

        return round($this->{$priceField} / constant('self::' . $marginConstant));
    }







    /***************************************
     * Pricing Adjustments For Marketplace
     * 
     * Return price multiplied by profit
     * margin.
     ****************************************/

    // regular price
    protected function guestPostPrice(): Attribute
    {
        return Attribute::make(
            get: fn(string $value) => round($value * self::PROFIT_MARGIN_MULTIPLIER_GENERAL),
        );
    }

    // casino
    protected function casinoPostPrice(): Attribute
    {
        return Attribute::make(
            get: fn(string $value) => round($value * self::PROFIT_MARGIN_MULTIPLIER_CASINO),
        );
    }

    // adult
    protected function adultPostPrice(): Attribute
    {
        return Attribute::make(
            get: fn(string $value) => round($value * self::PROFIT_MARGIN_MULTIPLIER_ADULT),
        );
    }

    // finance
    protected function financePostPrice(): Attribute
    {
        return Attribute::make(
            get: fn(string $value) => round($value * self::PROFIT_MARGIN_MULTIPLIER_FINANCE),
        );
    }

    // dating
    protected function datingPostPrice(): Attribute
    {
        return Attribute::make(
            get: fn(string $value) => round($value * self::PROFIT_MARGIN_MULTIPLIER_DATING),
        );
    }

    // cbd
    protected function cbdPostPrice(): Attribute
    {
        return Attribute::make(
            get: fn(string $value) => round($value * self::PROFIT_MARGIN_MULTIPLIER_CBD),
        );
    }

    // crypto
    protected function cryptoPostPrice(): Attribute
    {
        return Attribute::make(
            get: fn(string $value) => round($value * self::PROFIT_MARGIN_MULTIPLIER_CRYPTO),
        );
    }

    public function scopeActive($query)
    {
        return $query->where('active', 1);
    }
    //So you can use: MarketplaceWebsite::active()->get();


    public function outreach(): HasOne
    {
        return $this->hasOne(Outreach::class, 'marketplace_website_id');
    }
}
