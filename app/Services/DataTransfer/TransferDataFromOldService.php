<?php

namespace App\Services\DataTransfer;

use App\Jobs\DataTransfer\{Order, MainCategory, Website};
use App\Helpers\BatchJobManager;
use App\Models\MarketplaceOrder;
use App\Models\MarketplaceWebsite;
use Illuminate\Support\Facades\DB;

class TransferDataFromOldService
{

    public function __construct(private int $batchSize, private string $queue) {}

    public function transferDataFromOld()
    {
        try {
            // Transfer Categories
            // $query = DB::connection('old_db')->table('marketplace_website_categories');
            // $result['main_category'] = (new BatchJobManager(
            //     $this->batchSize,
            //     MainCategory::class,
            //     $query,
            //     $this->queue
            // ))();

            // Transfer Websites
            $query = MarketplaceWebsite::on('old_db')->withoutGlobalScopes();
            $result['website'] = (new BatchJobManager(
                $this->batchSize,
                Website::class,
                $query,
                $this->queue
            ))();


            // Transfer Orders
            $query = MarketplaceOrder::on('old_db')->withoutGlobalScopes();
            $result['orders'] = (new BatchJobManager(
                $this->batchSize,
                Order::class,
                $query,
                $this->queue
            ))();

            return $result;
        } catch (\Exception $e) {
            throw $e;
        }
    }
}
