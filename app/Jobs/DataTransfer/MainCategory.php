<?php

namespace App\Jobs\DataTransfer;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Support\Facades\DB;
use App\Models\MarketplaceWebsiteCategory;

class MainCategory implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(private int $batchSize, private int $offset) {}

    public function handle()
    {
        // Get the main categories from the old database
        $mainCategories = DB::connection('old_db')->table('marketplace_website_categories')->offset($this->offset)->limit($this->batchSize)->get();

        try {
            // -----------------------
            // Transfer the main categories to the new database
            // -----------------------  
            foreach ($mainCategories as $mainCategory) {
                MarketplaceWebsiteCategory::firstOrCreate((array) $mainCategory);
            }


            return [
                'success' => true,
                'dry_run' => false,
                'main_categories_count' => count($mainCategories),
                'message' => 'Main categories transferred successfully.'
            ];
        } catch (\Exception $e) {
            throw $e;
        }
    }
}
