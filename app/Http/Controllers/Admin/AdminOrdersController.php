<?php

namespace App\Http\Controllers\Admin;

use Domain\Order\List\GetOrdersWithFilters;
use Domain\Order\List\GetOrderItemWithDetails;
use Domain\Order\List\GetAssignmentFormData;
use Domain\Order\Actions\UpdateOrderItemState;
use Domain\Order\Actions\UpdateOrderItemContent;
use Domain\Order\Actions\UpdateOrderItemRequirements;
use Domain\Order\Actions\AssignWriterToOrderItem;
use Domain\Order\Actions\UpdateAssignment;
use Domain\Activity\List\GetOrderItemActivityLogs;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\View\View;
use Inertia\Inertia;
use App\Models\MarketplaceOrder;
use App\Http\Controllers\Controller;
use App\Enums\MarketplaceOrderStatus;
use Inertia\Response;
use App\Models\MarketplaceSingleOrderItem;
use App\Http\Requests\Admin\AdminOrdersIndexRequest;
use App\Http\Requests\Admin\UpdateOrderItemStateRequest;
use App\Http\Requests\Admin\UpdateOrderItemContentRequest;
use App\Http\Requests\Admin\UpdateOrderItemRequirementsRequest;
use App\Http\Requests\Admin\AssignWriterRequest;
use App\Http\Requests\Admin\UpdateAssignmentRequest;

class AdminOrdersController extends Controller
{

    /*********************************************************************
     * ADMIN ORDERS INDEX PAGE
     *********************************************************************
     *
     * Lists and filters orders for admin
     *
     * @param AdminOrdersIndexRequest $request  Filter and pagination params
     * @return Response                              Inertia response
     *********************************************************************/
    public function index(AdminOrdersIndexRequest $request): Response
    {
        // -----------------------
        // Get Inputs
        $inputs = $request->validated();


        // -----------------------
        // Fetch Orders
        $orders = (new GetOrdersWithFilters())($inputs);


        // -----------------------
        // Render Page
        return Inertia::render(
            'Admin/Orders/Index',
            [
                'orders'   => $orders->withQueryString(),
                'filters'  => array_merge(
                    $inputs,
                    ['perPage' => $request->input('perPage', config('pressbear.default_pagination_10'))]
                ),
                'statuses' => collect(MarketplaceOrderStatus::cases())
                    ->map(fn($case) => [
                        'label' => ucwords(str_replace(['-', '_'], ' ', $case->name)),
                        'value' => $case->value,
                    ]),
            ]
        );
    }





    /**********************************************************************
     * ADMIN ORDER DETAILS PAGE
     **********************************************************************
     *
     * Retrieves and renders order with grouped items
     *
     * @param MarketplaceOrder $order  The marketplace order
     * @return Response                        Inertia response
     **********************************************************************/
    public function orderDetails(MarketplaceOrder $order)
    {
        // -----------------------
        // Load Relations
        $order->load([
            'user',
            'payment',
        ])->loadCount('orderItems');


        // -----------------------
        // Group Items
        $groupedItems = [
            'pending'   => $order->orderItems->filter(fn($item) => $item->state->isPending())->values(),
            'completed' => $order->orderItems->filter(fn($item) => $item->state->isCompleted())->values(),
            'cancelled' => $order->orderItems->filter(fn($item) => $item->state->isCancelled())->values(),
        ];


        // -----------------------
        // Render Page
        return Inertia::render('Admin/Orders/Details/Index', [
            'order'     => $order,
            'pending'   => $groupedItems['pending'],
            'completed' => $groupedItems['completed'],
            'cancelled' => $groupedItems['cancelled'],
        ]);
    }





    /*********************************************************************
     * ADMIN ORDER ITEM DETAILS PAGE
     **********************************************************************
     *
     * Renders detailed information for order item
     *
     * @param MarketplaceOrder           $order  Parent order
     * @param MarketplaceSingleOrderItem $item   Specific order item
     * @return Response                              Inertia response
     *********************************************************************/
    public function orderItemDetails(MarketplaceOrder $order, MarketplaceSingleOrderItem $item): Response
    {
        // -----------------------
        // Get Item Details
        $orderItemDetails = (new GetOrderItemWithDetails())($order, $item);

        // -----------------------
        // Render Details Page
        return Inertia::render(
            'Admin/Orders/Details/SingleOrderDetails/Index',
            $orderItemDetails
        );
    }





    /**********************************************************************
     * ADMIN INVOICE GENERATION
     **********************************************************************
     * 
     * Generate and display an invoice for a marketplace order.
     * 
     * @param MarketplaceOrder $order
     * The marketplace order to generate invoice for
     *
     * 
     * @return View
     * Renders the invoice view
     * 
     *********************************************************************/
    public function adminInvoice(MarketplaceOrder $order)
    {
        return view('app.invoices.invoice', ['order' => $order, 'user' => $order->user]);
    }




    /*********************************************************************
     * UPDATE ORDER ITEM STATE
     *********************************************************************
     * Invoke domain action to set new state.
     * Return redirect with success flash.
     *
     * @param MarketplaceSingleOrderItem  $item
     * @param UpdateOrderItemStateRequest $request
     * @return RedirectResponse
     *********************************************************************/
    public function updateOrderItemState(
        MarketplaceSingleOrderItem $item,
        UpdateOrderItemStateRequest $request
    ): RedirectResponse {
        // Perform state update
        (new UpdateOrderItemState())(
            $item,
            $request->validated()['state']
        );


        // Redirect with success
        return back()->with('success', 'Order item state updated.');
    }




    /*********************************************************************
     * UPDATE ORDER ITEM REQUIREMENTS
     **********************************************************************
     * Update requirements for an order item.
     *
     * @param MarketplaceSingleOrderItem            $item
     * @param UpdateOrderItemRequirementsRequest    $request
     * @return RedirectResponse
     *********************************************************************/
    public function updateOrderItemRequirements(
        MarketplaceSingleOrderItem $item,
        UpdateOrderItemRequirementsRequest $request
    ): RedirectResponse {
        try {
            // Get requirements data
            $requirementsData = $request->getRequirementsData();

            // Update requirements
            (new UpdateOrderItemRequirements())($item, $requirementsData);

            return back()->with('success', 'Requirements updated.');
        } catch (\Illuminate\Validation\ValidationException $e) {
            // Validation errors
            return back()
                ->withErrors($e->validator)
                ->withInput();
        }
    }




    /*********************************************************************
     * UPDATE ORDER ITEM CONTENT
     **********************************************************************
     * Update content for an order item.
     *
     * @param MarketplaceSingleOrderItem $item
     * @param UpdateOrderItemContentRequest $request
     * @return RedirectResponse
     *********************************************************************/
    public function updateOrderItemContent(
        MarketplaceSingleOrderItem $item,
        UpdateOrderItemContentRequest $request
    ): RedirectResponse {
        try {
            // Get content data
            $contentData = $request->getContentData();


            // Update content
            (new UpdateOrderItemContent())($item, $contentData);


            return back()->with('success', 'Content updated.');
        } catch (\Illuminate\Validation\ValidationException $e) {
            // Validation errors
            return back()
                ->withErrors($e->validator)
                ->withInput();
        }
    }




    /*********************************************************************
     * ASSIGN WRITER
     **********************************************************************
     * Assign a writer to an order item.
     *
     * @param AssignWriterRequest          $request
     * @param MarketplaceSingleOrderItem   $item
     * @return JsonResponse
     *********************************************************************/
    public function assignWriter(
        AssignWriterRequest $request,
        MarketplaceSingleOrderItem $item
    ): JsonResponse {
        try {
            // Get writer ID
            $writerId = $request->getWriterId();

            // Assign writer
            (new AssignWriterToOrderItem())($item, $writerId);

            return response()->json(['message' => 'Writer assigned successfully.']);
        } catch (\Exception $e) {
            Log::error('Failed to assign writer: ' . $e->getMessage());
            return response()->json(['message' => 'Failed to assign writer.'], 500);
        }
    }




    /*********************************************************************
     * EDIT ASSIGNMENT
     **********************************************************************
     * Display assignment edit form.
     *
     * @param MarketplaceSingleOrderItem $item
     * @return Response
     *********************************************************************/
    public function editAssignment(
        MarketplaceSingleOrderItem $item
    ): Response {
        // Get assignment data
        $formData = (new GetAssignmentFormData())($item);

        // Render assignment form
        return Inertia::render(
            'Admin/Teams/Writers/AssignmentForm',
            $formData
        );
    }




    /*********************************************************************
     * UPDATE ASSIGNMENT
     **********************************************************************
     * Update assignment for an order item.
     *
     * @param UpdateAssignmentRequest      $request
     * @param MarketplaceSingleOrderItem   $item
     * @return RedirectResponse
     *********************************************************************/
    public function updateAssignment(
        UpdateAssignmentRequest $request,
        MarketplaceSingleOrderItem $item
    ): RedirectResponse {
        try {
            // Get assignment data
            $assignmentData = $request->getAssignmentData();

            // Update assignment
            (new UpdateAssignment())($item, $assignmentData);

            // Redirect on success
            return redirect()
                ->route('admin.writers.assignments')
                ->with('success', 'Assignment updated successfully.');
        } catch (\Exception $e) {
            // Redirect on error
            return redirect()
                ->back()
                ->with('error', 'Failed to update assignment.')
                ->withInput();
        }
    }




    /*********************************************************************
     * ORDER ITEM ACTIVITY LOGS
     **********************************************************************
     *
     * Fetch activity logs for given order item
     *
     * @param int $orderItemId
     * @return JsonResponse
     *
     *********************************************************************/
    public function getActivityLogsForOrderItem(int $orderItemId): JsonResponse
    {
        // -----------------------
        // Get Activity Logs
        $result = (new GetOrderItemActivityLogs())($orderItemId);

        return response()->json($result);
    }
}
