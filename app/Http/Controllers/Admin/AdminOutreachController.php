<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Domain\Stats\Lists\Outreach\GetOutreachStats;
use Domain\Stats\Requests\OutreachStatsRequest;
use Inertia\Inertia;

class AdminOutreachController extends Controller
{



    /**********************************************************************
     * OUTREACH PERFORMANCE DASHBOARD
     **********************************************************************
     *
     * Displays outreach team performance metrics using Domain Stats services
     * - with proper validation and business logic separation.
     *
     * @param OutreachStatsRequest $request
     * Validated request containing date filtering parameters
     *
     * @return \Inertia\Response
     * Renders the outreach performance dashboard with user stats
     **********************************************************************/
    public function stats(OutreachStatsRequest $request): \Inertia\Response
    {
        // -----------------------
        // Get Filter Data
        $filters = $request->getFilters();

        // -----------------------
        // Get Outreach User Stats
        $users = (new GetOutreachUserStats())($filters);

        // -----------------------
        // Render Dashboard
        return Inertia::render('Admin/Teams/Outreach/Index', [
            'users' => $users,
            'filters' => $filters,
        ]);
    }






}
