<?php

namespace App\Http\Controllers\Admin;

use Inertia\Inertia;
use App\Models\Outreach;
use Illuminate\Http\Request;
use App\Services\OutreachService;
use App\Http\Controllers\Controller;
use App\Models\MarketplaceAdminWebsite;
use App\Models\User;
use App\Enums\Role;
use Domain\Stats\Requests\OutreachStatsRequest;
use Domain\Stats\Lists\GetOutreachUserStats;

class AdminOutreachController extends Controller
{



    /**********************************************************************
     * OUTREACH PERFORMANCE STATS (REFACTOR ‼️)
     **********************************************************************
     *
     * Retrieves a paginated list of all outreach users along with counts
     * of their assigned websites by status (inprogress, onboarded, rejected).
     *
     * Applies optional date filters based on preset ranges or custom date range
     * using the `applyDateFilter()` helper.
     *
     * @param Request $request
     * Incoming request containing date filtering parameters
     *
     * @return \Inertia\Response
     * Renders the outreach performance dashboard with user stats
     **********************************************************************/
    public function stats(Request $request)
    {
        $query = User::where('role', Role::Outreach->value)
            ->withCount([
                'outreaches as inprogress_count' => fn($q) => $this->applyDateFilter($q->where('status', 'inprogress'), $request, 'inprogress'),
                'outreaches as onboarded_count' => fn($q) => $this->applyDateFilter($q->where('status', 'onboarded'), $request, 'onboarded'),
                'outreaches as rejected_count' => fn($q) => $this->applyDateFilter($q->where('status', 'rejected'), $request, 'rejected'),
            ])
            ->orderByDesc('id');

        $users = $query->paginate(10)->withQueryString();
        
        return Inertia::render('Admin/Teams/Outreach/Index', [
            'users' => $users,
            'filters' => [
                'preset_range' => $request->input('preset_range', 'show_all'),
                'start_date' => $request->input('start_date'),
                'end_date' => $request->input('end_date'),
            ],
        ]);
    }





    /**********************************************************************
     * APPLY DATE FILTER TO OUTREACH STATS (DUPLICATE HERE AND IN HELPER ‼️)
     **********************************************************************
     *
     * Helper function creation and use it every where from that place.
     * 
     * Dynamically applies date filters to a given query based on the outreach
     * status and selected date range (preset or custom).
     *
     * Uses different timestamp columns depending on outreach status:
     * - 'created_at' for inprogress
     * - 'onboarded_at' for onboarded
     * - 'rejected_at' for rejected
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * The Eloquent query to apply filters on
     *
     * @param Request $request
     * The request containing filtering parameters
     *
     * @param string $status
     * The outreach status being filtered (inprogress, onboarded, rejected)
     *
     * @return \Illuminate\Database\Eloquent\Builder
     * Returns the modified query with applied date constraints
     **********************************************************************/
    private function applyDateFilter($query, Request $request, string $status)
    {
        $preset = $request->input('preset_range');
        $start = $request->input('start_date');
        $end = $request->input('end_date');

        // Determine the timestamp column based on status
        $dateColumn = match ($status) {
            'inprogress' => 'created_at',
            'onboarded' => 'onboarded_at',
            'rejected' => 'rejected_at',
            default => 'created_at',
        };

        if ($preset && $preset !== 'custom') {
            $query->when(true, function ($q) use ($preset, $dateColumn) {
                return match ($preset) {
                    'today' => $q->whereDate($dateColumn, today()),
                    'yesterday' => $q->whereDate($dateColumn, today()->subDay()),
                    'last_7_days' => $q->whereBetween($dateColumn, [now()->subDays(6)->startOfDay(), now()->endOfDay()]),
                    'last_30_days' => $q->whereBetween($dateColumn, [now()->subDays(29)->startOfDay(), now()->endOfDay()]),
                    'last_90_days' => $q->whereBetween($dateColumn, [now()->subDays(89)->startOfDay(), now()->endOfDay()]),
                    'last_12_months' => $q->whereBetween($dateColumn, [now()->subMonths(12)->startOfDay(), now()->endOfDay()]),
                    default => $q,
                };
            });
        }

        if ($preset === 'custom') {
            if ($start) {
                $query->whereDate($dateColumn, '>=', $start);
            }
            if ($end) {
                $query->whereDate($dateColumn, '<=', $end);
            }
        }

        return $query;
    }
}
