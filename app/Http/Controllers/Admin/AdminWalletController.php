<?php

namespace App\Http\Controllers\Admin;

use Inertia\Inertia;
use Illuminate\Http\Request;
use App\Models\MarketplaceOrder;
use App\Models\WalletWithdrawalRequest;
use App\Http\Controllers\Controller;
use Bavix\Wallet\Models\Transaction;

class AdminWalletController extends Controller
{

    /********************************************************************** 
     * ADMIN WALLET INDEX PAGE
     **********************************************************************
     * 
     * Retrieves and displays all wallet transactions with pagination.
     * 
     *********************************************************************/
    public function index(Request $request)
    {
        // Get recent transactions (paginated)
        $transactions = Transaction::with('wallet', 'wallet.holder')
            ->orderBy('id', 'desc')
            ->paginate(15)
            ->through(function ($transaction) {
                return [
                    'id' => $transaction->id, //id of the transaction
                    'type' => $transaction->type, //type of the transaction
                    'amount' => $transaction->amount, //amount of the transaction
                    'user' => $transaction->wallet->holder, //user who made the transaction
                    'order_id' => $transaction->meta['order_id'] ?? 'N/A', //order id if transaction is related to an order
                    'reference' => $transaction->meta['reference'] ?? 'N/A', // reference if transaction is related to a reference
                    'status' => $transaction->confirmed ? 'Approved' : 'Pending', // status of the transaction
                    'date' => $transaction->created_at->format('Y-m-d H:i:s'), // date of the transaction
                    'debit' => $transaction->type === 'withdraw' ? number_format($transaction->amount / 100, 2) : 0, // debit amount
                    'credit' => $transaction->type === 'deposit' ? number_format($transaction->amount / 100, 2) : 0, // credit amount
                ];
            });

        // Get transaction statistics
        $stats = [
            'total_deposits' => number_format(MarketplaceOrder::sum('price_paid'), 2),
            'total_withdrawals' => number_format(WalletWithdrawalRequest::sum('amount') / 100, 2),
            'pending_transactions' => Transaction::where('confirmed', false)
                ->count(),
        ];


        return Inertia::render('Admin/Wallet/Index', [
            'transactions' => $transactions,
            'stats' => $stats,
        ]);
    }





    /**********************************************************************
     * ADMIN PAYMENT SHOW PAGE
     **********************************************************************
     * 
     * Retrieves and displays detailed payment information including 
     * ...user and order details.
     * 
     * Loads user and order data with eager loading of order items count.
     * 
     * @param MarketplacePayment $payment The payment to display details for
     * @return \Inertia\Response Renders the admin payment show page with payment details
     *
     *********************************************************************/
    public function show(Transaction $transaction)
    {
        return Inertia::render('Admin/Wallet/Show', [
            'transaction' => $transaction,
        ]);
    }
}
