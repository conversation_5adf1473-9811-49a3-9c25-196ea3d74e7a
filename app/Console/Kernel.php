<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;


//---------------------------------
//          KERNEL FILE
//--------------------------------
class Kernel extends ConsoleKernel
{

    /*******************************************************
     * SCHEDULE COMMAND RUNS
     *******************************************************
     * Commands here are introduced to scheduling system.
     * And run as part of command -> artisan schedule:run
     * duration is piped.
     * <PERSON><PERSON> auto handles it.
    /*******************************************************/
    protected function schedule(Schedule $schedule): void
    {
        $schedule->command('cart:delete-old-items')->daily(); //> 60d delete
        $schedule->command('website:update-seo-metrics')->daily();
        // Stats calculations
        // old activity logs delete
    }



    /*******************************************************
     * REGISTER COMMANDS
     *******************************************************
     * Register the commands for the application.
     * And run as part of command -> artisan schedule:run
    /*******************************************************/
    protected function commands(): void
    {
        $this->load(__DIR__ . '/Commands');
        require base_path('routes/console.php');
    }



    // ----------------------------------------------------
    // MANUAL REGISTERATION
    // Can be used to register artisan commands manually 
    // (but usually you let load() function handle it).
    protected $commands = [];
}
