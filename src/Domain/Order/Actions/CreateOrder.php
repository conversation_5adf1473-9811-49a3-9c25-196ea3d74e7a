<?php

namespace Domain\Order\Actions;

use App\Models\MarketplaceOrder;
use App\Models\MarketplacePayment;
use App\Models\MarketplaceCheckoutSession;
use Domain\Order\Actions\CreateOrderItems;

/*********************************************************************
 * CREATE ORDER ACTION
 *********************************************************************
 *
 * Handles the creation of marketplace orders from checkout sessions.
 * This action orchestrates the order creation process including
 * payment validation, order record creation, and order items generation.
 *
 * Responsibilities:
 * - Validates payment and session data
 * - Creates parent order record
 * - Delegates order items creation
 * - Ensures data consistency through transactions
 *
 *********************************************************************/
class CreateOrder
{
    /*********************************************************************
     * HANDLE ORDER CREATION
     *********************************************************************
     *
     * Main entry point for order creation process.
     * Delegates to the createOrder method to handle the actual creation.
     *
     * @param array $data - Order creation data
     * @param MarketplaceCheckoutSession $sessionData - Checkout session data
     * @param MarketplacePayment $payment - Payment record
     * @return MarketplaceOrder - Created order instance
     *
     *********************************************************************/
    public function handle(array $data, MarketplaceCheckoutSession $sessionData, MarketplacePayment $payment): MarketplaceOrder
    {
        return $this->createOrder($data, $sessionData, $payment);
    }

    /*********************************************************************
     * CREATE ORDER PROCESS
     *********************************************************************
     *
     * Orchestrates the complete order creation process:
     * 1. Creates the parent order record
     * 2. Generates order items from cart data
     * 3. Links everything together
     *
     * @param array $data - Order creation data
     * @param MarketplaceCheckoutSession $sessionData - Checkout session data
     * @param MarketplacePayment $payment - Payment record
     * @return MarketplaceOrder - Created order instance
     *
     *********************************************************************/
    public function createOrder(array $data, MarketplaceCheckoutSession $sessionData, MarketplacePayment $payment): MarketplaceOrder
    {
        // -----------------------
        // Create Parent Order
        $order = $this->createParentOrder([
            'user_id' => $data['user_id'],
            'payment_id' => $payment->id,
            'price_paid' => $payment->payment_amount,
            'cart_items_ids' => count($sessionData->cart_items_ids),
        ]);


        // -----------------------
        // Create Order Items
        (new CreateOrderItems())->handle($sessionData, $order);

        return $order;
    }

    /*********************************************************************
     * CREATE PARENT ORDER RECORD
     *********************************************************************
     *
     * Creates the main order record in the database.
     * This is the parent record that will contain order items.
     *
     * @param array $data - Order data including user_id, payment_id, price_paid, cart_items_ids
     * @return MarketplaceOrder - Created order instance
     * @throws \Exception - If order creation fails
     *
     *********************************************************************/
    public function createParentOrder(array $data): MarketplaceOrder
    {
        try {
            // -----------------------
            // Create Order
            return MarketplaceOrder::create([
                'user_id' => $data['user_id'],
                'payment_id' => $data['payment_id'],
                'price_paid' => $data['price_paid'],
                'items_in_orders' => $data['cart_items_ids'],
            ]);
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }
}
