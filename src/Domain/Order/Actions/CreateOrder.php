<?php

namespace Domain\Order\Actions;

use App\Models\MarketplaceOrder;
use App\Models\MarketplacePayment;
use App\Models\MarketplaceCheckoutSession;
use Domain\Order\Actions\CreateOrderItems;

class CreateOrder
{
    /*********************************************************************
     * CREATE ORDER - PROCESS ORDER CREATION
     *********************************************************************
     *
     * Handles order creation by delegating to the Payment class.
     * - Validates and processes order data
     * - Creates order record in database
     *
     *********************************************************************/


    public function handle(array $data, MarketplaceCheckoutSession $sessionData, MarketplacePayment $payment): MarketplaceOrder
    {
        return $this->createOrder($data, $sessionData, $payment);
    }

    /*********************************************************************
     * CREATE ORDER - PROCESS ORDER CREATION
     *********************************************************************
     *
     * Handles order creation by delegating to the Payment class.
     * - Validates and processes order data
     * - Creates order record in database
     *
     *********************************************************************/
    public function createOrder(array $data, MarketplaceCheckoutSession $sessionData, MarketplacePayment $payment): MarketplaceOrder
    {
        // -----------------------
        // Create Parent Order
        $order = $this->createParentOrder([
            'user_id' => $data['user_id'],
            'payment_id' => $payment->id,
            'price_paid' => $payment->payment_amount,
            'cart_items_ids' => count($sessionData->cart_items_ids),
        ]);


        // -----------------------
        // Create Order Items
        (new CreateOrderItems())->handle($sessionData, $order);

        return $order;
    }

    /*********************************************************************
     * CREATE ORDER - PROCESS ORDER CREATION
     *********************************************************************
     *
     * Handles order creation by delegating to the Payment class.
     * - Validates and processes order data
     * - Creates order record in database
     *
     *********************************************************************/

    public function createParentOrder(array $data): MarketplaceOrder
    {
        try {
            // -----------------------
            // Create Order
            return MarketplaceOrder::create([
                'user_id' => $data['user_id'],
                'payment_id' => $data['payment_id'],
                'price_paid' => $data['price_paid'],
                'items_in_orders' => $data['cart_items_ids'],
            ]);
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }
}
