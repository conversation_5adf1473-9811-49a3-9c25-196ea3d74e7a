<?php

namespace Domain\Order\Actions;

use App\Models\MarketplaceSingleOrderItem;

class StateTransitions
{
    /*************************************************
     * Handle
     *
     * Transitioning the state of the order item to the next state.
     *
     * @param MarketplaceSingleOrderItem $orderItem
     * @param string $state
     * @return MarketplaceSingleOrderItem
    /************************************************/
    public function handle(MarketplaceSingleOrderItem $orderItem, string $state): MarketplaceSingleOrderItem
    {
        $orderItem->state->transitionTo($state);
        return $orderItem;
    }
}
