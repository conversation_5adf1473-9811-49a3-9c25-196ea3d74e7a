<?php

namespace Domain\Order;

use Domain\Payment\Payment;
use App\Models\MarketplaceOrder;
use Illuminate\Support\Facades\DB;
use Domain\Order\Actions\CreateOrder;
use Domain\Payment\Enum\PaymentStatus;
use Domain\CheckoutSession\CheckoutSession;
use Domain\Payment\Exceptions\PaymentFailedException;
use Domain\Order\Exceptions\OrderAlreadyExistsException;

class Order
{
    /*************************************************
     * Constructor
    /************************************************/
    public function __construct(
        private CreateOrder $createOrder,
        private Payment $payment,
        private CheckoutSession $checkoutSession
    ) {}

    /*************************************************
     * Create Order
    /************************************************/
    public function create(array $data): MarketplaceOrder|string
    {
        DB::beginTransaction();
        try {
            // -----------------------
            // Check if order already exists
            $order = $this->payment->getPayment($data['payment_intent_id']);

            if (isset($order) && $order->order()->exists()) {
                throw new OrderAlreadyExistsException();
            }

            // -----------------------
            // Check payment status
            $paymentStatus = $this->payment->getPaymentStatus($data['payment_intent_id'], env('PAYMENT_METHOD', 'stripe'));
            if ($paymentStatus->status !== 'succeeded') {
                throw new PaymentFailedException();
            }


            // -----------------------
            // Get Checkout Session
            $sessionData = $this->checkoutSession->GetByPaymentIntentId($data['payment_intent_id'], $data['user_id']);

            // -----------------------
            // Create Payment
            $payment = $this->payment->create([
                'user_id' => $data['user_id'],
                'payment_intent_id' => $data['payment_intent_id'],
                'total_amount' => $sessionData->total_amount,
                'status' => PaymentStatus::Paid->value,
            ]);


            // -----------------------
            // Create order & order items
            $order = $this->createOrder->handle($data, $sessionData, $payment);

            // -----------------------
            // Update Session and Cleanup
            $this->checkoutSession->checkoutSuccess($sessionData, $order);

            DB::commit();
            return $order;
        } catch (\Exception $e) {
            DB::rollBack();
            throw new \Exception('Failed to create order');
        }
    }
}
