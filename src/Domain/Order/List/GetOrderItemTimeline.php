<?php

namespace Domain\Order\List;

use App\Enums\OrderItemStates;
use App\Models\MarketplaceSingleOrderItem;
use Illuminate\Support\Collection;

class GetOrderItemTimeline
{
    /**
     * @var array<int, array{state: string, label: string}>
     */
    protected array $steps = [
        ['state' => OrderItemStates::RequirementsPending->value,                  'label' => 'Order Placed'],
        ['state' => OrderItemStates::RequirementAwaitingPublisherApproval->value, 'label' => 'Requirements'],
        ['state' => OrderItemStates::ContentPending->value,                       'label' => 'Approve Requirements'],
        ['state' => OrderItemStates::ContentAwaitingPublisherApproval->value,     'label' => 'Content'],
        ['state' => OrderItemStates::PublicationInProcess->value,                 'label' => 'Approval'],
        ['state' => OrderItemStates::PublicationDelivered->value,                 'label' => 'Published'],
        ['state' => OrderItemStates::OrderItemCompleted->value,                   'label' => 'Completed'],
        ['state' => OrderItemStates::OrderItemCancelled->value,                   'label' => 'Cancelled'],
    ];

    /*******************************************************************
     * ORDER ITEM TIMELINE
     *********************************************************************
     *
     * Builds a sequential timeline of order-item steps.
     * Determines the current state index.
     * Marks all steps up to and including current as completed.
     * Returns an array of ['id','label','state','completed'] entries.
     *
     * @param  MarketplaceSingleOrderItem  $orderItem
     *   The order item whose state drives the timeline.
     *
     * @return array<int, array{id: int, label: string, state: string, completed: bool}>
     *   A list of timeline steps with completion flags.
     *
     *******************************************************************/
    public function handle(MarketplaceSingleOrderItem $orderItem): array
    {
        // Find the zero-based index of the current state
        $currentIndex = Collection::make($this->steps)
            ->pluck('state')
            ->search($orderItem->state_name, true);

        // Build and return the timeline, auto-assigning IDs and completion flags
        return Collection::make($this->steps)
            ->values() // ensure continuous integer keys
            ->map(fn(array $step, int $index): array => [
                'id'        => $index + 1,
                'label'     => $step['label'],
                'state'     => $step['state'],
                'completed' => $index <= $currentIndex,
            ])
            ->all();
    }
}
