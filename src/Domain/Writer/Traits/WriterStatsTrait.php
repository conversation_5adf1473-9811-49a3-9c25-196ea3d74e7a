<?php

namespace Domain\Writer\Traits;

use App\Enums\OrderItemStates;
use App\Models\MarketplaceSingleOrderItem;

trait WriterStatsTrait
{

    private array $contentStates;

    public function __construct()
    {
        $this->contentStates = [
            OrderItemStates::ContentPending->value,
            OrderItemStates::ContentAssignedToWriter->value,
            OrderItemStates::ContentAdvertiserReview->value,
            OrderItemStates::ContentRevisionRequestedByAdvertiser->value,
            OrderItemStates::ContentAwaitingPublisherApproval->value,
        ];
    }





    /**********************************
     * GET IN PROGRESS COUNT
     ***********************************/
    private function getInProgressCount(): int
    {
        return MarketplaceSingleOrderItem::whereIn('state', [
            OrderItemStates::ContentAssignedToWriter->value,
        ])->count();
    }




    /**********************************
     * GET PENDING ASSIGNMENT COUNT
     ***********************************/
    private function getPendingAssignmentCount(): int
    {
        return MarketplaceSingleOrderItem::whereIn('state', $this->contentStates)
            ->whereDoesntHave('content')
            ->count();
    }





    /**********************************
     * GET IN REVISION COUNT
     ***********************************/
    private function getInRevisionCount(): int
    {
        return MarketplaceSingleOrderItem::whereIn('state', [
            OrderItemStates::ContentAdvertiserReview->value,
            OrderItemStates::ContentRevisionRequestedByAdvertiser->value,
            OrderItemStates::ContentAwaitingPublisherApproval->value,
        ])->count();
    }





    /**********************************
     * GET UPCOMING COUNT
     ***********************************/
    private function getUpcomingCount(): int
    {
        return MarketplaceSingleOrderItem::whereNotIn('state', $this->contentStates)
            ->where(function ($q) {
                $q->where('is_content_provided_by_customer', true)
                    ->orWhereDoesntHave('content')
                    ->orWhereHas('content', fn($c) => $c->whereNull('writer_id'));
            })->count();
    }
}
