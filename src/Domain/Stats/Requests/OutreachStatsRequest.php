<?php

namespace Domain\Stats\Requests;

use Illuminate\Foundation\Http\FormRequest;

class OutreachStatsRequest extends FormRequest
{
    /*********************************************************************
     * DETERMINE IF USER IS AUTHORIZED
     *********************************************************************/
    public function authorize(): bool
    {
        return true;
    }


    /*********************************************************************
     * GET VALIDATION RULES
     *********************************************************************/
    public function rules(): array
    {
        return [
            'preset_range' => 'nullable|string|in:show_all,today,yesterday,last_7_days,last_30_days,last_90_days,last_12_months,custom',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ];
    }


    /*********************************************************************
     * GET FILTER DATA
     **********************************************************************
     *
     * Extracts and returns the validated filter data from the request.
     *
     * @return array
     *********************************************************************/
    public function getFilters(): array
    {
        return [
            'preset_range' => $this->input('preset_range', 'show_all'),
            'start_date' => $this->input('start_date'),
            'end_date' => $this->input('end_date'),
        ];
    }
}
