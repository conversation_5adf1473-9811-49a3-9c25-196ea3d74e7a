<?php

namespace Domain\Stats\Lists;

use App\Models\User;
use App\Enums\Role;
use App\Traits\Filters\DateFilterTrait;

class GetOutreachUserStats
{
    use DateFilterTrait;

    /*********************************************************************
     * GET OUTREACH USER STATS
     **********************************************************************
     *
     * Retrieves paginated outreach users with their performance stats
     * - using existing date filtering patterns.
     *
     * > Uses existing DateFilterTrait for filtering
     * > Builds optimized queries with withCount
     * > Handles pagination consistently
     *
     * @param array $filters
     * @return \Illuminate\Pagination\LengthAwarePaginator
     *********************************************************************/
    public function __invoke(array $filters): \Illuminate\Pagination\LengthAwarePaginator
    {
        // -----------------------
        // Build Base Query
        $query = User::where('role', Role::Outreach->value)
            ->withCount([
                'outreaches as inprogress_count' => function ($q) use ($filters) {
                    $this->applyOutreachDateFilter($q->where('status', 'inprogress'), $filters, 'inprogress');
                },
                'outreaches as onboarded_count' => function ($q) use ($filters) {
                    $this->applyOutreachDateFilter($q->where('status', 'onboarded'), $filters, 'onboarded');
                },
                'outreaches as rejected_count' => function ($q) use ($filters) {
                    $this->applyOutreachDateFilter($q->where('status', 'rejected'), $filters, 'rejected');
                },
            ])
            ->orderByDesc('id');

        // -----------------------
        // Return Paginated Results
        return $query->paginate(10)->withQueryString();
    }


    /*********************************************************************
     * APPLY OUTREACH DATE FILTER
     **********************************************************************
     *
     * Applies date filters to outreach queries based on status-specific
     * - timestamp columns using existing DateFilterTrait patterns.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param array $filters
     * @param string $status
     * @return \Illuminate\Database\Eloquent\Builder
     *********************************************************************/
    private function applyOutreachDateFilter($query, array $filters, string $status)
    {
        // -----------------------
        // Determine Date Column Based on Status
        $dateColumn = match ($status) {
            'inprogress' => 'created_at',
            'onboarded' => 'onboarded_at',
            'rejected' => 'rejected_at',
            default => 'created_at',
        };

        // -----------------------
        // Apply Date Filter Using Existing Trait
        return $this->applyDateFilter($query, $filters, $dateColumn);
    }
}
