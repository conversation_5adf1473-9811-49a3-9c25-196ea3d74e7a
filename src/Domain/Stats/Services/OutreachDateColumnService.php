<?php

namespace Domain\Stats\Services;

class OutreachDateColumnService
{
    /*********************************************************************
     * GET DATE COLUMN FOR STATUS
     **********************************************************************
     *
     * Returns the appropriate date column for outreach status filtering.
     * Centralizes the status-to-column mapping logic.
     *
     * @param string $status
     * @return string
     *********************************************************************/
    public static function getDateColumn(string $status): string
    {
        return match ($status) {
            'inprogress' => 'created_at',
            'onboarded' => 'onboarded_at',
            'rejected' => 'rejected_at',
            default => 'created_at',
        };
    }


    /*********************************************************************
     * GET ALL STATUS COLUMNS
     **********************************************************************
     *
     * Returns mapping of all outreach statuses to their date columns.
     *
     * @return array
     *********************************************************************/
    public static function getAllStatusColumns(): array
    {
        return [
            'inprogress' => 'created_at',
            'onboarded' => 'onboarded_at',
            'rejected' => 'rejected_at',
        ];
    }
}
