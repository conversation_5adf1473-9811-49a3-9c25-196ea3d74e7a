<?php

namespace Domain\Stats;

use Domain\Stats\Lists\GetOutreachStats;
use Domain\Stats\Lists\GetSalesStats;
use Domain\Stats\Lists\GetUserStats;

// Stats domain facade
class Stats
{
    // OUTREACH STATS
    public static function outreach(int $userId, array $filters = []): array
    {
        return (new GetOutreachStats())($userId, $filters);
    }




    // SALES STATS
    public static function sales(): GetSalesStats
    {
        return new GetSalesStats();
    }




    // USER STATS
    public static function user(): GetUserStats
    {
        return new GetUserStats();
    }
}
