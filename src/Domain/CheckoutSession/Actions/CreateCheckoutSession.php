<?php

namespace Domain\CheckoutSession\Actions;

use Illuminate\Support\Arr;
use Domain\Payment\Payment;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Domain\Cart\Cart as CartService;
use App\Models\MarketplaceCheckoutSession;
use Illuminate\Support\Facades\Session;
use Illuminate\Http\RedirectResponse;

class CreateCheckoutSession
{
    private CartService $cartService;
    private Payment $payment;

    public function __construct()
    {
        $this->cartService = new CartService();
        $this->payment = new Payment();
    }

    /*********************************************************************
     * CREATE CHECKOUT SESSION
     **********************************************************************
     *
     * We Create a checkout session to handle payment and order creation 
     * once user proceed to payment page:
     * 
     * 1. We collect all the current item ids in cart.
     * 2. Calculate amount to charge.
     * 3. Create a payment intent with stripe for processing payment.
     * 4. Store checkout session in db to use it as reference point 
     *  ..for order creation after order success.
     * 
     *
     *********************************************************************/
    public function handle()
    {
        try {
            // -----------------------
            // Get Cart Data
            $cartData = $this->cartService->getUserCart();


            // -----------------------
            // Check: if Cart is Empty send to home route.
            if (
                empty($cartData['items']) ||
                ($cartData['stats']['count'] < 1 ||
                    $cartData['stats']['totalPrice'] < 1)
            ) {
                return redirect()->route('home');
            }

            // -----------------------
            // Create Stripe Payment Intent
            $paymentIntent = $this->payment->createPaymentIntent(
                $cartData['stats']['totalPrice'],
                Auth::user(),
                env('PAYMENT_METHOD', 'stripe')
            );
            $cartData['stripeClientSecret'] = $paymentIntent->client_secret;


            // -----------------------
            // Pick Cart Items
            $cartItemsIds = Arr::pluck($cartData['items'], 'cart_item_id');


            // --------------------------------
            // Create & Store Checkout Session
            $cartData['Session'] = MarketplaceCheckoutSession::create([
                'user_id' => $cartData['user_id'],
                'cart_items_ids' => $cartItemsIds,
                'total_amount' => $cartData['stats']['totalPrice'],
                'total_items' => $cartData['stats']['count'],
                'order_memo' => Session::get('order_memo') ?? null,
                'checkout_ip_address' => request()->ip() ?? null,
                'stripe_payment_intent' => $paymentIntent->id,
            ]);

            return $cartData;
        } catch (\Exception $e) {
            throw new \Exception('Session Creation Error: ' . $e->getMessage());
        }
    }
}
