<?php

namespace Domain\CheckoutSession\Actions;

use App\Models\MarketplaceOrder;
use App\Models\MarketplaceCheckoutSession;
use Domain\Order\Notifications\OrderCreated;
use Domain\Cart\Cart;
use Illuminate\Support\Facades\Session;

/*********************************************************************
 * CHECKOUT SUCCESS ACTION
 *********************************************************************
 *
 * Handles post-checkout success operations for the marketplace.
 * This action performs cleanup and notification tasks after a
 * successful checkout and order creation.
 *
 * Responsibilities:
 * - Marks checkout session as successful
 * - Cleans up cart items that were converted to orders
 * - Removes order memo from session
 * - Sends order creation notifications
 * - Ensures proper cleanup of temporary data
 *
 *********************************************************************/
class CheckoutSuccess
{
    private Cart $cart;

    public function __construct()
    {
        $this->cart = new Cart();
    }

    /***************************************************************
     * CHECKOUT SUCCESSFUL
     ***************************************************************
     *
     * If checkout was successful then we:
     * 1. Mark it as success (so session can't be used again)
     * 2. Delete order items from cart.
     * 3. Forget order memo.
     * 4. Send order creation email.
     * 
     * @param $sessionData MarketplaceCheckoutSession
     * @param $order MarketplaceOrder
     * @return true.
     * 
    /**************************************************************/
    public function handle(MarketplaceCheckoutSession $sessionData, MarketplaceOrder $order)
    {
        // ---------------------------
        // Update Session and Cleanup
        $sessionData->update(['checkout_successful' => true]);
        $this->cart->deleteCartItems($sessionData->cart_items_ids);


        // -----------------------------
        // Delete Order Memo From Session
        Session::forget('order_memo');


        // ---------------------------
        // Send Order Notification
        $sessionData->user->notify(new OrderCreated($order));

        return true;
    }
}
