<?php

namespace Domain\CheckoutSession\Actions;

use App\Models\MarketplaceOrder;
use App\Models\MarketplaceCheckoutSession;
use Domain\Order\Notifications\OrderCreated;
use Domain\Cart\Cart;
use Illuminate\Support\Facades\Session;

class CheckoutSuccess
{
    private Cart $cart;

    public function __construct()
    {
        $this->cart = new Cart();
    }

    /***************************************************************
     * CHECKOUT SUCCESSFUL
     ***************************************************************
     *
     * If checkout was successful then we:
     * 1. Mark it as success (so session can't be used again)
     * 2. Delete order items from cart.
     * 3. Forget order memo.
     * 4. Send order creation email.
     * 
     * @param $sessionData MarketplaceCheckoutSession
     * @param $order MarketplaceOrder
     * @return true.
     * 
    /**************************************************************/
    public function handle(MarketplaceCheckoutSession $sessionData, MarketplaceOrder $order)
    {
        // ---------------------------
        // Update Session and Cleanup
        $sessionData->update(['checkout_successful' => true]);
        $this->cart->deleteCartItems($sessionData->cart_items_ids);


        // -----------------------------
        // Delete Order Memo From Session
        Session::forget('order_memo');


        // ---------------------------
        // Send Order Notification
        $sessionData->user->notify(new OrderCreated($order));

        return true;
    }
}
