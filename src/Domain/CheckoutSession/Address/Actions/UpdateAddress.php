<?php

namespace Domain\CheckoutSession\Address\Actions;

use App\Models\User;
use Illuminate\Support\Facades\Log;

class UpdateAddress
{
    /*********************************************************************
     * UPDATE ADDRESS
     **********************************************************************
     *
     * Updates the user's address information.
     *
     * @param array $data The data to update the address with.
     * 
     *********************************************************************/
    public function handle(array $data, User $user): bool
    {
        // -----------------------
        // Handle address data - convert to JSON structure
        $addressData = processAddressData($data);

        try {
            // -----------------------
            // Update User Address
            $updateAddress = $user->update($addressData);

            return $updateAddress;
        } catch (\Exception $e) {
            Log::error('Error updating address of user: ' . $user->id . ' - ' . $e->getMessage());
            return false;
        }
    }
}
