<?php

namespace Domain\Seo\Helpers;

use App\Models\MarketplaceWebsiteCategory;
use App\Models\MarketplaceWebsite;
use App\Models\Keyword;

class DataHelper
{


    /*********************************************************************
     * FIND CATEGORY FROM DB TO ATTACH TO SITE
     * 
     * get category data from similarweb
     * find the category id from db
     *
     * @param $category string (default: other)
     *********************************************************************/
    public static function findCategoryID($categoryText = 'other')
    {

        $categoryText   = str_replace('_', ' ', $categoryText); //replace _
        $haveSubCategory = strpos($categoryText, '/');

        if ($haveSubCategory) {
            $categorise = explode('/', $categoryText);
            $category['parent'] = $categorise[0];
            $category['child']  = $categorise[1];
        } else {
            $category['parent'] = $categoryText;
        }


        // find category by child or parent
        if ($haveSubCategory) {
            $findCategory = MarketplaceWebsiteCategory::where('category', $category['child'])->first();
        } else {
            $findCategory = MarketplaceWebsiteCategory::where('category', $category['parent'])->first();
        }

        // if no category found - (other category id)
        if (!$findCategory) {
            $findCategory = MarketplaceWebsiteCategory::where('category', 'other')->first();
        }

        $categoryID = $findCategory->getKey();

        return $categoryID;
    }



    /**********************************************
     * Create & Attach Keywords
     * 
     * Create and attach topics, if exist then
     * update and attach.
     * 
     * @param $topics Array
     * @param $website MarketplaceWebsite
     * 
     * @return results
     **/
    public static function attachKeywords(MarketplaceWebsite $website, $keywords)
    {

        // Insert or update keywords
        // updating because we need to update keyword volumes n cpc in future
        foreach ($keywords as $keyword) {
            Keyword::upsert(
                [
                    'name'     => $keyword['Name'],
                    'cpc'      => self::normaliseNumber($keyword['Cpc']),
                    'volume'   => $keyword['Volume']
                ],
                ['name'], //unique
                ['cpc', 'volume']
            ); //to update
        }

        // again call and get ids
        $keywordNames   = collect($keywords)->pluck('Name');
        $KeywordIDs     = Keyword::whereIn('name', $keywordNames)->select('id')->get();


        // Attach topics
        $sync_keywords = $website->keyword_website()->sync($KeywordIDs);

        return $sync_keywords;
    }



    /*********************************************************************
     * NORMALISE NUMBER
     * 
     * Normalise the number to a percentage
     * 
     * @param $number float
     * @return float
     *********************************************************************/
    public static function normaliseNumber($number)
    {
        return round($number * 100, 2);
    }
}
