<?php

declare(strict_types=1);

namespace Domain\Seo\Actions;

use App\Models\MarketplaceWebsite;
use Domain\Seo\Enums\SEOSourceType;
use Domain\Seo\Services\SEOHttpClient;
use Illuminate\Support\Facades\DB;

class UpdateWebsiteSeo
{

    /*********************************************************************
     * CREATE WEBSITE SEO
     *********************************************************************
     *
     * Handles website SEO creation by delegating to the SEOHttpClient class.
     * - Calls the SEO API
     * - Updates the website SEO & logs
     *
     *********************************************************************/
    public function __construct(private MarketplaceWebsite $website, private SEOSourceType $sourceType, private string $type) {}

    /*********************************************************************
     * CREATE WEBSITE SEO
     *********************************************************************/
    public function __invoke()
    {
        DB::beginTransaction();
        try {
            // Call the API
            $seoHttpClient = new SEOHttpClient($this->website, $this->sourceType, $this->type);
            $response = $seoHttpClient->handle();


            // Update Website Seo & logs
            if ($response instanceof \Illuminate\Http\Client\Response) {
                $data = $response->json()['data'];
                // Update Website Seo
                (new UpdateSeoData())($this->website, $data, $this->sourceType, $this->type);

                // Update logs
                $data = [
                    'source' => $this->sourceType->value,
                    'type' => $this->type,
                    'record_updated_at' => now(),
                    'is_success' => true,
                    'data' => json_encode($response->json()),
                    'error_message' => null,
                ];

                (new UpdateLogs())($this->website, $data);
            }
            DB::commit();
            return true;
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }
    }
}
