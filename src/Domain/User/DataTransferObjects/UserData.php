<?php

declare(strict_types=1);

namespace Domain\User\DataTransferObjects;

use Spatie\LaravelData\Data;
use Spatie\LaravelData\Attributes\Validation\Rule;
use App\Rules\ValidRole;

class UserData extends Data
{
    public function __construct(
        public string $name,
        #[Rule('email')]
        public string $email,
        public ?string $password,
        #[Rule(new ValidRole)]
        public string $role,
        #[Rule('numeric')]
        public ?string $phone,
        #[Rule('exists:countries_list,id')]
        public ?int $country_id,
        public ?string $company,
        public ?string $address,
        public ?string $city,
        public ?string $postal_code,
        public ?bool $email_verified = null,
    ) {}
}
