<?php

namespace Domain\Cart\Actions;

use App\Models\User;
use App\Models\MarketplaceWebsite;
use App\Models\MarketplaceCartItem;
use Domain\Cart\Actions\DeleteCartItem;
use Domain\Cart\DataTransferObjects\UpdateCartData;

class UpdateCart
{


    /*************************************************
     * Handle
    /************************************************/
    public function __invoke(UpdateCartData $payload, User $user): bool
    {
        /***********************************************
         * 1. CART ITEM NICHE CHANGE
         * i.e: regular to casino niche
         * 
         * 1). validate input
         * 2). check if website accepts requested niche
         * 3). check if cart Item exists
         * 4). update niche
         * 
         * @return true/false
         * *********************************************/

        switch ($payload->task) {
            case 'updateNiche':
                return $this->updateNiche($payload, $user);
            case 'deleteCartItem':
                return $this->deleteCartItem($payload, $user);
            case 'updateContentWriting':
                return $this->updateContentWriting($payload, $user);
            default:
                return false;
        }
    }

    /*************************************************
     * Update Niche
     * @param  UpdateCartData $payload
     * @return bool
    /************************************************/
    public function updateNiche(UpdateCartData $payload, User $user): bool
    {
        try {

            $cartItem = $this->findCartItem($payload, $user);


            if (!($cartItem instanceof MarketplaceCartItem)) {
                return false;
            }

            $cartItem->update(['niche' => $payload->niche]);

            return true;
        } catch (\Throwable $th) {
            return false;
        }
    }

    /*************************************************
     * Delete Cart Item
     * @param  UpdateCartData $payload
     * @return bool
    /************************************************/
    public function deleteCartItem(UpdateCartData $payload, User $user): bool
    {
        return (new DeleteCartItem())($payload, $user);
    }

    /*************************************************
     * Update Content Writing
     * @param  UpdateCartData $payload
     * @return bool
    /************************************************/
    public function updateContentWriting(UpdateCartData $payload, User $user): bool
    {
        try {
            $cartWebsiteId = $payload->cartWebsiteId;
            $contentWriting = $payload->contentWriting;
            $userId = $user->id;

            $cartItem = MarketplaceCartItem::where('user_id', $userId)
                ->where('marketplace_website_id', $cartWebsiteId)
                ->first();

            if (!$cartItem) {
                return false;
            }

            $cartItem->update(['content_writing' => $contentWriting]);

            return true;
        } catch (\Throwable $th) {
            return false;
        }
    }

    /*************************************************
     * Update Cart Item
     * @param  UpdateCartData $payload
     * @return bool
    /************************************************/
    public function findCartItem(UpdateCartData $payload, User $user): MarketplaceCartItem|null
    {

        $cartWebsiteId  = $payload->cartWebsiteId;
        $userId         = $user->id;
        $niche          = $payload->niche;


        $acceptsNiche = MarketplaceWebsite::find($cartWebsiteId)
            ->acceptsNiche($niche);


        if (!$acceptsNiche) {
            return null;
        }

        $cartItem = MarketplaceWebsite::find($cartWebsiteId)
            ->inCart()
            ->where('user_id', $userId)
            ->first();

        if (!$cartItem) {
            return null;
        }

        return $cartItem;
    }
}
