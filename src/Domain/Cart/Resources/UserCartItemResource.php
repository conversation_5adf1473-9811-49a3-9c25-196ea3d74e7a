<?php

namespace Domain\Cart\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class UserCartItemResource extends JsonResource
{
    /*************************************************
     * toArray
    /************************************************/
    public function toArray($request)
    {
        return [
            'website_id'       => $this->website->id,
            'cart_item_id'     => $this->id,
            'website'          => $this->website->website_domain,
            'niche'            => $this->niche,
            'acceptNiches'     => $this->website->acceptsNicheList(),
            'content_writing'  => $this->content_writing,
            'price'            => $this->website->nichePrice($this->niche) ?? 0,
        ];
    }
}
