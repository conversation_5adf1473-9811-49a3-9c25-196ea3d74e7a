<?php

namespace Domain\Cart\List;

use App\Models\User;
use Domain\Cart\Resources\UserCartItemResource;

class GetUserCart
{
    public static function handle(User $user): array
    {
        $user->load('cartItems');
        $cartItems = $user->cartItems->map(function ($item) {
            return UserCartItemResource::make($item)->resolve();
        })->toArray();

        // Calculate totals
        $totalPrice = array_sum(array_column($cartItems, 'price'));


        $cartData = [
            'items'   => $cartItems,
            'stats'   => [
                'totalPrice' => $totalPrice,
                'count'      => count($cartItems),
            ],
            'user_id' => $user->id,
        ];

        return $cartData;
    }
}
