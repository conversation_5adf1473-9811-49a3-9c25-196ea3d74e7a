<?php

namespace Domain\Cart;

use Domain\Cart\Actions\CreateCart;
use Domain\Cart\Actions\UpdateCart;
use Domain\Cart\Actions\DeleteCartItems;
use Domain\Cart\List\GetUserCart;
use Illuminate\Support\Facades\Auth;
use Domain\Cart\DataTransferObjects\CartData;
use Domain\Cart\DataTransferObjects\UpdateCartData;

class Cart
{

    /*************************************************
     * Add to Cart
    /************************************************/
    public function addToCart(CartData $payload): bool
    {
        return (new CreateCart())($payload, Auth::user());
    }

    /*************************************************
     * Update Cart Item
    /************************************************/
    public function updateCart(UpdateCartData $payload): bool
    {
        return (new UpdateCart())($payload, Auth::user());
    }

    /*************************************************
     * Delete Cart Items
    /************************************************/
    public function deleteCartItems(array $itemsIds): bool
    {
        return (new DeleteCartItems())->handle($itemsIds);
    }

    /*************************************************
     * Get User Cart
    /************************************************/
    public static function getUserCart(): array
    {
        return GetUserCart::handle(Auth::user());
    }
}
