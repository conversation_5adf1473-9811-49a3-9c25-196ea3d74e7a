<?php

declare(strict_types=1);

namespace Domain\Cart\DataTransferObjects;

use Spa<PERSON>\LaravelData\Data;
use App\Rules\ValidNiche;
use Spatie\LaravelData\Attributes\Validation\Rule;

class UpdateCartData extends Data
{
    public function __construct(
        #[Rule('integer|exists:marketplace_websites,id')]
        public int $cartWebsiteId,
        #[Rule(new ValidNiche)]
        public ?string $niche,
        public ?bool $contentWriting,
        #[Rule('in:updateNiche,deleteCartItem,updateContentWriting')]
        public string $task,
    ) {}
}
