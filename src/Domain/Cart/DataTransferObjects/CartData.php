<?php

declare(strict_types=1);

namespace Domain\Cart\DataTransferObjects;

use Spatie\LaravelData\Data;
use App\Rules\ValidNiche;
use Spatie\LaravelData\Attributes\Validation\Rule;

class CartData extends Data
{
    public function __construct(
        #[Rule('integer|exists:marketplace_websites,id')]
        public int $website,
        #[Rule(new ValidNiche)]
        public string $niche,
    ) {}
}
