{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.1", "barryvdh/laravel-dompdf": "^3.1", "bavix/laravel-wallet": "^11.4", "erag/laravel-disposable-email": "^3.3", "guzzlehttp/guzzle": "^7.2", "inertiajs/inertia-laravel": "^2.0", "jeremykendall/php-domain-parser": "^6.0", "laravel/framework": "^11.0", "laravel/jetstream": "^5.0", "laravel/pulse": "^1.0@beta", "laravel/reverb": "^1.0", "laravel/tinker": "^2.8", "league/flysystem-aws-s3-v3": "^3.29", "livewire/livewire": "^3.4", "pusher/pusher-php-server": "^7.2", "sentry/sentry-laravel": "^4.13", "spatie/laravel-activitylog": "^4.10", "spatie/laravel-data": "^4.17", "spatie/laravel-honeypot": "^4.5", "spatie/laravel-model-states": "^2.11", "stripe/stripe-php": "^12.1", "symfony/http-client": "^7.1", "symfony/mailgun-mailer": "^7.1", "tightenco/ziggy": "^2.5"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.9", "fakerphp/faker": "^1.9.1", "larastan/larastan": "^3.0", "laravel/pint": "^1.22", "laravel/sail": "^1.18", "laravel/telescope": "^5.2", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^8.1", "phpstan/phpstan": "^2.1", "phpunit/phpunit": "^10.1", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Domain\\": "src/Domain/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helpers/Helpers.php", "app/Helpers/Platform.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"], "phpstan:test": "phpstan analyse src tests", "phpstan:test:memory": "phpstan analyse src tests --memory-limit=1G"}, "extra": {"laravel": {"dont-discover": ["laravel/telescope"]}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "beta", "prefer-stable": true}